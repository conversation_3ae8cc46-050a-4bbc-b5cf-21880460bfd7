#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试知识库服务

验证修复后的知识库服务是否正常工作
"""
import asyncio
import json
from backend.app.iot.service.knowledge_base_service import knowledge_base_service
from backend.app.iot.schema.knowledge_base import KnowledgeBaseCreate, KnowledgeBaseQuery


async def test_health_check():
    """测试健康检查"""
    print("=" * 50)
    print("测试知识库服务健康检查")
    print("=" * 50)
    
    try:
        health_status = await knowledge_base_service.health_check()
        print(f"健康检查结果:")
        print(json.dumps(health_status, indent=2, ensure_ascii=False))
        
        if health_status["status"] == "healthy":
            print("✅ 知识库服务健康检查成功！")
            return True
        else:
            print("❌ 知识库服务健康检查失败")
            return False
            
    except Exception as e:
        print(f"❌ 健康检查异常: {str(e)}")
        return False


async def test_list_knowledge_bases():
    """测试获取知识库列表"""
    print("\n" + "=" * 50)
    print("测试获取知识库列表")
    print("=" * 50)
    
    try:
        # 创建查询参数
        query = KnowledgeBaseQuery(
            page=1,
            page_size=5,
            orderby="create_time",
            desc=True
        )
        
        # 模拟用户ID
        user_id = "test_user_123"
        
        # 获取知识库列表
        kb_list = await knowledge_base_service.list_knowledge_bases(query, user_id)
        
        print(f"获取到 {len(kb_list)} 个知识库:")
        for i, kb in enumerate(kb_list, 1):
            print(f"{i}. {kb.name} (ID: {kb.id})")
            print(f"   描述: {kb.description or '无描述'}")
            print(f"   文档数: {kb.document_count}, 块数: {kb.chunk_count}")
            print(f"   嵌入模型: {kb.embedding_model}")
            print(f"   创建时间: {kb.create_date}")
            print()
        
        print("✅ 获取知识库列表成功！")
        return True
        
    except Exception as e:
        print(f"❌ 获取知识库列表失败: {str(e)}")
        return False


async def test_create_knowledge_base():
    """测试创建知识库"""
    print("\n" + "=" * 50)
    print("测试创建知识库")
    print("=" * 50)
    
    try:
        # 创建知识库数据
        kb_data = KnowledgeBaseCreate(
            name=f"FastAPI_Service_Test_{int(asyncio.get_event_loop().time())}",
            description="FastAPI知识库服务测试",
            permission="me",
            chunk_method="naive"
            # 不指定embedding_model，使用默认值
        )
        
        # 模拟用户ID
        user_id = "test_user_123"
        
        print(f"正在创建知识库: {kb_data.name}")
        
        # 创建知识库
        kb_info = await knowledge_base_service.create_knowledge_base(kb_data, user_id)
        
        print(f"✅ 知识库创建成功！")
        print(f"ID: {kb_info.id}")
        print(f"名称: {kb_info.name}")
        print(f"描述: {kb_info.description}")
        print(f"嵌入模型: {kb_info.embedding_model}")
        print(f"创建时间: {kb_info.create_date}")
        
        return kb_info.id
        
    except Exception as e:
        print(f"❌ 创建知识库失败: {str(e)}")
        return None


async def main():
    """主测试函数"""
    print("🚀 开始测试知识库服务...")
    
    # 测试结果统计
    results = {
        "health_check": False,
        "list_knowledge_bases": False,
        "create_knowledge_base": False
    }
    
    # 测试1: 健康检查
    results["health_check"] = await test_health_check()
    
    # 如果健康检查失败，跳过其他测试
    if not results["health_check"]:
        print("\n⚠️  健康检查失败，跳过其他测试")
    else:
        # 测试2: 获取知识库列表
        results["list_knowledge_bases"] = await test_list_knowledge_bases()
        
        # 测试3: 创建知识库
        created_kb_id = await test_create_knowledge_base()
        results["create_knowledge_base"] = created_kb_id is not None
    
    # 输出测试结果汇总
    print("\n" + "=" * 60)
    print("测试结果汇总:")
    print("=" * 60)
    
    for test_name, success in results.items():
        status = "✅ 成功" if success else "❌ 失败"
        print(f"{test_name.replace('_', ' ').title()}: {status}")
    
    success_count = sum(results.values())
    total_count = len(results)
    
    print(f"\n总体结果: {success_count}/{total_count} 项测试通过")
    
    if success_count == total_count:
        print("🎉 所有测试通过！知识库服务运行正常。")
    elif success_count > 0:
        print("⚠️  部分测试通过，请检查失败的测试项。")
    else:
        print("❌ 所有测试失败，请检查服务配置和网络连接。")
    
    print("=" * 60)


if __name__ == "__main__":
    asyncio.run(main())
