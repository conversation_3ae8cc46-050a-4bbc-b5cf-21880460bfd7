#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试JWT集成

验证FastAPI服务是否能正确解析Java后端生成的JWT token
"""
import asyncio
import json
import time
import uuid
from datetime import datetime, timedelta
import jwt
import httpx
from backend.common.security.iot_adapter import iot_adapter


def create_test_jwt_token():
    """
    创建一个模拟Java后端格式的JWT token
    """
    # Java后端的JWT配置
    secret_key = "abcdefghijklfastbeesmartrstuvwxyz"
    algorithm = "HS512"
    
    # 生成UUID作为login_user_key（模拟Java后端）
    login_user_key = str(uuid.uuid4())
    
    # 创建payload（模拟Java后端的token结构）
    now = datetime.utcnow()
    expire_time = now + timedelta(hours=24)  # 24小时过期
    
    payload = {
        "login_user_key": login_user_key,  # Java后端的关键字段
        "userid": "123",  # 用户ID
        "username": "test_user",  # 用户名
        "iat": int(now.timestamp()),  # 签发时间
        "exp": int(expire_time.timestamp()),  # 过期时间
        "jti": login_user_key  # JWT ID
    }
    
    # 生成JWT token
    token = jwt.encode(payload, secret_key, algorithm=algorithm)
    
    print(f"生成的测试JWT token:")
    print(f"Token: {token}")
    print(f"Login User Key: {login_user_key}")
    print(f"Payload: {json.dumps(payload, indent=2)}")
    print()
    
    return token, login_user_key


async def test_iot_adapter():
    """
    测试IoT适配器解析JWT token
    """
    print("=" * 60)
    print("测试IoT适配器JWT解析")
    print("=" * 60)
    
    # 创建测试token
    token, login_user_key = create_test_jwt_token()
    
    try:
        # 测试IoT适配器
        if not iot_adapter.is_enabled():
            print("❌ IoT集成未启用")
            return False
        
        print("✅ IoT集成已启用")
        print(f"配置验证: {iot_adapter.validate_config()}")
        
        # 解析token
        token_payload = iot_adapter.decode_iot_jwt(token)
        
        print("✅ JWT token解析成功!")
        print(f"用户ID: {token_payload.user_id}")
        print(f"用户名: {token_payload.username}")
        print(f"会话ID: {token_payload.session_id}")
        print(f"过期时间: {token_payload.expire_time}")
        
        return True
        
    except Exception as e:
        print(f"❌ JWT token解析失败: {str(e)}")
        return False


async def test_api_with_jwt():
    """
    测试使用JWT token调用API
    """
    print("\n" + "=" * 60)
    print("测试API JWT认证")
    print("=" * 60)
    
    # 创建测试token
    token, login_user_key = create_test_jwt_token()
    
    # 测试API端点
    test_endpoints = [
        "/api/v1/iot/kb/health",
        "/api/v1/iot/kb/knowledge-bases?page=1&page_size=5"
    ]
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    async with httpx.AsyncClient(
        timeout=httpx.Timeout(30.0, connect=10.0),
        verify=False,
        follow_redirects=True
    ) as client:
        
        for endpoint in test_endpoints:
            try:
                print(f"\n测试端点: {endpoint}")
                
                response = await client.get(
                    f"http://127.0.0.1:8000{endpoint}",
                    headers=headers
                )
                
                print(f"状态码: {response.status_code}")
                
                if response.status_code == 200:
                    data = response.json()
                    print(f"✅ 请求成功")
                    print(f"响应: {json.dumps(data, indent=2, ensure_ascii=False)}")
                elif response.status_code == 401:
                    print(f"❌ 认证失败 (401)")
                    print(f"响应: {response.text}")
                else:
                    print(f"⚠️  其他状态码: {response.status_code}")
                    print(f"响应: {response.text}")
                    
            except Exception as e:
                print(f"❌ 请求异常: {str(e)}")


async def test_manual_jwt_decode():
    """
    手动测试JWT解码
    """
    print("\n" + "=" * 60)
    print("手动JWT解码测试")
    print("=" * 60)
    
    # 创建测试token
    token, login_user_key = create_test_jwt_token()
    
    try:
        # 手动解码
        secret_key = "abcdefghijklfastbeesmartrstuvwxyz"
        algorithm = "HS512"
        
        payload = jwt.decode(token, secret_key, algorithms=[algorithm])
        
        print("✅ 手动JWT解码成功!")
        print(f"完整payload: {json.dumps(payload, indent=2)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 手动JWT解码失败: {str(e)}")
        return False


async def main():
    """
    主测试函数
    """
    print("🚀 开始JWT集成测试...")
    
    # 测试结果
    results = {
        "manual_decode": False,
        "iot_adapter": False,
        "api_auth": False
    }
    
    # 测试1: 手动JWT解码
    results["manual_decode"] = await test_manual_jwt_decode()
    
    # 测试2: IoT适配器
    results["iot_adapter"] = await test_iot_adapter()
    
    # 测试3: API认证（只有在前面测试成功时才进行）
    if results["iot_adapter"]:
        await test_api_with_jwt()
        results["api_auth"] = True  # 这里简化处理，实际应该检查API响应
    
    # 输出测试结果汇总
    print("\n" + "=" * 60)
    print("JWT集成测试结果汇总:")
    print("=" * 60)
    
    for test_name, success in results.items():
        status = "✅ 成功" if success else "❌ 失败"
        print(f"{test_name.replace('_', ' ').title()}: {status}")
    
    success_count = sum(results.values())
    total_count = len(results)
    
    print(f"\n总体结果: {success_count}/{total_count} 项测试通过")
    
    if success_count == total_count:
        print("🎉 JWT集成测试完全成功！")
        print("现在可以使用Java后端生成的JWT token访问FastAPI服务了。")
    elif success_count > 0:
        print("⚠️  部分测试通过，请检查失败的测试项。")
    else:
        print("❌ 所有测试失败，请检查JWT配置。")
    
    print("=" * 60)


if __name__ == "__main__":
    asyncio.run(main())
