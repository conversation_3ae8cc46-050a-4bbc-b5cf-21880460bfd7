#!/usr/bin/env python3
"""
测试Java系统是否使用相同的Redis
"""
import asyncio
import time
from backend.database.redis import redis_client

async def test_java_redis_connection():
    """测试Java系统是否使用相同的Redis"""
    print("=" * 60)
    print("测试Java系统Redis连接")
    print("=" * 60)
    
    # 1. 写入一个测试key
    test_key = f"fastapi_test_{int(time.time())}"
    test_value = "FastAPI测试数据"
    
    print(f"1. 写入测试数据: {test_key} = {test_value}")
    await redis_client.setex(test_key, 300, test_value)  # 5分钟过期
    
    # 2. 读取验证
    stored_value = await redis_client.get(test_key)
    print(f"2. 读取验证: {stored_value}")
    
    if stored_value == test_value:
        print("✅ Redis连接正常")
    else:
        print("❌ Redis连接异常")
        return
    
    # 3. 检查Java系统的配置缓存
    print(f"\n3. 检查Java系统配置缓存:")
    java_keys = [
        "sys_config:sys.user.initPassword",
        "sys_config:sys.index.skinName", 
        "sys_dict:sys_user_sex",
        "sys_dict:sys_show_hide",
    ]
    
    for key in java_keys:
        value = await redis_client.get(key)
        if value:
            print(f"✅ {key}: 存在")
        else:
            print(f"❌ {key}: 不存在")
    
    # 4. 监控新的login token
    print(f"\n4. 监控login token (请在Java系统中登录):")
    print("监控中... (按Ctrl+C停止)")
    
    try:
        last_keys = set()
        while True:
            # 搜索login相关的keys
            current_keys = set()
            async for key in redis_client.scan_iter(match="login_*"):
                current_keys.add(key)
            
            # 检查新增的keys
            new_keys = current_keys - last_keys
            if new_keys:
                print(f"\n🎉 发现新的login keys:")
                for key in new_keys:
                    print(f"  - {key}")
                    # 尝试获取值
                    try:
                        value = await redis_client.get(key)
                        if value and len(value) < 200:
                            print(f"    值: {value}")
                        else:
                            print(f"    值长度: {len(value) if value else 0}")
                    except Exception as e:
                        print(f"    获取值失败: {e}")
            
            last_keys = current_keys
            await asyncio.sleep(2)  # 每2秒检查一次
            
    except KeyboardInterrupt:
        print(f"\n监控结束")
    
    # 5. 清理测试数据
    await redis_client.delete(test_key)
    print(f"清理测试数据: {test_key}")

if __name__ == "__main__":
    asyncio.run(test_java_redis_connection())
