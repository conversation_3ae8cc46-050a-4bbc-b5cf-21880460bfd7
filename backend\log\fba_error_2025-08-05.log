2025-08-04 16:19:46.551 | ERROR    | 77df56496ac64b5599d594f837a65cad | 请求异常: Not Authenticated
2025-08-04 16:19:46.579 | ERROR    | c4e63e914e9e4e1481c1a16e9a023d1f | 请求异常: Not Authenticated
2025-08-04 16:20:51.542 | ERROR    | 8ca988b31cae4b319d20a3a6cb9d5ba7 | 请求异常: ResponseBase.fail() got an unexpected keyword argument 'code'
2025-08-04 16:20:51.544 | ERROR    | 8ca988b31cae4b319d20a3a6cb9d5ba7 | Exception in ASGI application

Traceback (most recent call last):

  File "<string>", line 1, in <module>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\multiprocessing\spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 372
               │     └ 3
               └ <function _main at 0x000002B106C07B00>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\multiprocessing\spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 372
           │    └ <function BaseProcess._bootstrap at 0x000002B106B07A60>
           └ <SpawnProcess name='SpawnProcess-1' parent=34812 started>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\multiprocessing\process.py", line 314, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x000002B106B06FC0>
    └ <SpawnProcess name='SpawnProcess-1' parent=34812 started>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x000002B10900E870>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-1' parent=34812 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-1' parent=34812 started>
    │    └ <function subprocess_started at 0x000002B10909C5E0>
    └ <SpawnProcess name='SpawnProcess-1' parent=34812 started>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=484, family=2, type=1, proto=6, laddr=('127.0.0.1', 8000)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x000002B1091C2CF0>>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=484, family=2, type=1, proto=6, laddr=('127.0.0.1', 8000)>]
           │       │   │    └ <function Server.serve at 0x000002B10907FB00>
           │       │   └ <uvicorn.server.Server object at 0x000002B1091C2CF0>
           │       └ <function run at 0x000002B1067D0680>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 194, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x000002B1091AACE0>
           │      └ <function Runner.run at 0x000002B108D80B80>
           └ <asyncio.runners.Runner object at 0x000002B106B81070>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-p...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x000002B108D7E660>
           │    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x000002B106B81070>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 674, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x000002B108D7E5C0>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 641, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000002B108D80400>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1986, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000002B108D18360>
    └ <Handle Task.task_wakeup(<Future finished result=None>)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future finished result=None>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future finished result=None>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future finished result=None>)>
> File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\protocols\http\httptools_impl.py", line 409, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
                   └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x000002B1091C3E90>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\middleware\proxy_headers.py", line 60, in __call__
    return await self.app(scope, receive, send)
                 │    │   │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002B10F...
                 │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002B...
                 │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
                 │    └ <fastapi.applications.FastAPI object at 0x000002B10D0EDD00>
                 └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x000002B1091C3E90>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\fastapi\applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
                           │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002B10F...
                           │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002B...
                           └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\applications.py", line 113, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002B10F...
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002B...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000002B10F94C230>
          └ <fastapi.applications.FastAPI object at 0x000002B10D0EDD00>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\errors.py", line 186, in __call__
    raise exc
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000002B10FCA7F60>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002B...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ CorrelationIdMiddleware(app=<backend.middleware.access_middleware.AccessMiddleware object at 0x000002B10F9D8FB0>, header_name...
          └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000002B10F94C230>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\asgi_correlation_id\middleware.py", line 90, in __call__
    await self.app(scope, receive, handle_outgoing_request)
          │    │   │      │        └ <function CorrelationIdMiddleware.__call__.<locals>.handle_outgoing_request at 0x000002B10FCA7CE0>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002B...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <backend.middleware.access_middleware.AccessMiddleware object at 0x000002B10F9D8FB0>
          └ CorrelationIdMiddleware(app=<backend.middleware.access_middleware.AccessMiddleware object at 0x000002B10F9D8FB0>, header_name...
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 182, in __call__
    with recv_stream, send_stream, collapse_excgroups():
         │            │            └ <function collapse_excgroups at 0x000002B10AAFD9E0>
         │            └ MemoryObjectSendStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_receive...
         └ MemoryObjectReceiveStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_rece...
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\contextlib.py", line 158, in __exit__
    self.gen.throw(value)
    │    │   │     └ ExceptionGroup('unhandled errors in a TaskGroup', [TypeError("ResponseBase.fail() got an unexpected keyword argument 'code'")])
    │    │   └ <method 'throw' of 'generator' objects>
    │    └ <generator object collapse_excgroups at 0x000002B10FA87850>
    └ <contextlib._GeneratorContextManager object at 0x000002B10FB2AFC0>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\_utils.py", line 83, in collapse_excgroups
    raise exc
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 184, in __call__
    response = await self.dispatch_func(request, call_next)
                     │    │             │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x000002B10FCA7B00>
                     │    │             └ <starlette.middleware.base._CachedRequest object at 0x000002B10FB2ABA0>
                     │    └ <bound method AccessMiddleware.dispatch of <backend.middleware.access_middleware.AccessMiddleware object at 0x000002B10F9D8FB0>>
                     └ <backend.middleware.access_middleware.AccessMiddleware object at 0x000002B10F9D8FB0>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\middleware\access_middleware.py", line 34, in dispatch
    response = await call_next(request)
                     │         └ <starlette.middleware.base._CachedRequest object at 0x000002B10FB2ABA0>
                     └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x000002B10FCA7B00>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 159, in call_next
    raise app_exc
          └ TypeError("ResponseBase.fail() got an unexpected keyword argument 'code'")
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x000002B10FB47F60>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002B10FCA7BA0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x000002B10F51C4D0>
          └ <backend.middleware.access_middleware.AccessMiddleware object at 0x000002B10F9D8FB0>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\cors.py", line 85, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x000002B10FB47F60>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002B10FCA7BA0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.authentication.AuthenticationMiddleware object at 0x000002B10F51D220>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x000002B10F51C4D0>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\authentication.py", line 48, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x000002B10FB47F60>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002B10FCA7BA0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <backend.middleware.state_middleware.StateMiddleware object at 0x000002B10FA5C7A0>
          └ <starlette.middleware.authentication.AuthenticationMiddleware object at 0x000002B10F51D220>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 182, in __call__
    with recv_stream, send_stream, collapse_excgroups():
         │            │            └ <function collapse_excgroups at 0x000002B10AAFD9E0>
         │            └ MemoryObjectSendStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_receive...
         └ MemoryObjectReceiveStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_rece...
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\contextlib.py", line 158, in __exit__
    self.gen.throw(value)
    │    │   │     └ ExceptionGroup('unhandled errors in a TaskGroup', [TypeError("ResponseBase.fail() got an unexpected keyword argument 'code'")])
    │    │   └ <method 'throw' of 'generator' objects>
    │    └ <generator object collapse_excgroups at 0x000002B10FA87370>
    └ <contextlib._GeneratorContextManager object at 0x000002B10FB50DD0>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\_utils.py", line 83, in collapse_excgroups
    raise exc
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 184, in __call__
    response = await self.dispatch_func(request, call_next)
                     │    │             │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x000002B10FB47D80>
                     │    │             └ <starlette.middleware.base._CachedRequest object at 0x000002B10FB50DA0>
                     │    └ <bound method StateMiddleware.dispatch of <backend.middleware.state_middleware.StateMiddleware object at 0x000002B10FA5C7A0>>
                     └ <backend.middleware.state_middleware.StateMiddleware object at 0x000002B10FA5C7A0>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\middleware\state_middleware.py", line 32, in dispatch
    response = await call_next(request)
                     │         └ <starlette.middleware.base._CachedRequest object at 0x000002B10FB50DA0>
                     └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x000002B10FB47D80>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 159, in call_next
    raise app_exc
          └ TypeError("ResponseBase.fail() got an unexpected keyword argument 'code'")
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x000002B10FB47B00>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002B10FB47A60>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <backend.middleware.opera_log_middleware.OperaLogMiddleware object at 0x000002B10FA5E8D0>
          └ <backend.middleware.state_middleware.StateMiddleware object at 0x000002B10FA5C7A0>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 182, in __call__
    with recv_stream, send_stream, collapse_excgroups():
         │            │            └ <function collapse_excgroups at 0x000002B10AAFD9E0>
         │            └ MemoryObjectSendStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_receive...
         └ MemoryObjectReceiveStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_rece...
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\contextlib.py", line 158, in __exit__
    self.gen.throw(value)
    │    │   │     └ ExceptionGroup('unhandled errors in a TaskGroup', [TypeError("ResponseBase.fail() got an unexpected keyword argument 'code'")])
    │    │   └ <method 'throw' of 'generator' objects>
    │    └ <generator object collapse_excgroups at 0x000002B10FA879F0>
    └ <contextlib._GeneratorContextManager object at 0x000002B10FC32240>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\_utils.py", line 83, in collapse_excgroups
    raise exc
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 184, in __call__
    response = await self.dispatch_func(request, call_next)
                     │    │             │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x000002B10FAB5080>
                     │    │             └ <starlette.middleware.base._CachedRequest object at 0x000002B10FC32030>
                     │    └ <bound method OperaLogMiddleware.dispatch of <backend.middleware.opera_log_middleware.OperaLogMiddleware object at 0x000002B1...
                     └ <backend.middleware.opera_log_middleware.OperaLogMiddleware object at 0x000002B10FA5E8D0>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\middleware\opera_log_middleware.py", line 115, in dispatch
    raise error from None
          └ TypeError("ResponseBase.fail() got an unexpected keyword argument 'code'")

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\middleware\opera_log_middleware.py", line 51, in dispatch
    response = await call_next(request)
                     │         └ <starlette.middleware.base._CachedRequest object at 0x000002B10FC32030>
                     └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x000002B10FAB5080>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 159, in call_next
    raise app_exc
          └ TypeError("ResponseBase.fail() got an unexpected keyword argument 'code'")
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x000002B10FC3A3E0>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002B10FC3A660>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000002B10F93C560>
          └ <backend.middleware.opera_log_middleware.OperaLogMiddleware object at 0x000002B10FA5E8D0>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\exceptions.py", line 63, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x000002B10FC3A3E0>
          │                            │    │    │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002B10FC3A660>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x000002B10FCB4230>
          │                            │    └ <fastapi.routing.APIRouter object at 0x000002B10DA4C3B0>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000002B10F93C560>
          └ <function wrap_app_handling_exceptions at 0x000002B10ABE3420>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002B10FB49300>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002B10FC3A660>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          └ <fastapi.routing.APIRouter object at 0x000002B10DA4C3B0>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\routing.py", line 716, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002B10FB49300>
          │    │                │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002B10FC3A660>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x000002B10DA4C3B0>>
          └ <fastapi.routing.APIRouter object at 0x000002B10DA4C3B0>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\routing.py", line 736, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002B10FB49300>
          │     │      │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002B10FC3A660>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │     └ <function Route.handle at 0x000002B10AC049A0>
          └ APIRoute(path='/api/v1/iot/kb/health', name='knowledge_base_health_check', methods=['GET'])
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\routing.py", line 290, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002B10FB49300>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002B10FC3A660>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <function request_response.<locals>.app at 0x000002B10FA48A40>
          └ APIRoute(path='/api/v1/iot/kb/health', name='knowledge_base_health_check', methods=['GET'])
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\routing.py", line 78, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
          │                            │    │        │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002B10FB49300>
          │                            │    │        │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002B10FC3A660>
          │                            │    │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │                            │    └ <starlette.requests.Request object at 0x000002B10FCB4680>
          │                            └ <function request_response.<locals>.app.<locals>.app at 0x000002B10FB48860>
          └ <function wrap_app_handling_exceptions at 0x000002B10ABE3420>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002B10FB48360>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002B10FC3A660>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          └ <function request_response.<locals>.app.<locals>.app at 0x000002B10FB48860>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\routing.py", line 75, in app
    response = await f(request)
                     │ └ <starlette.requests.Request object at 0x000002B10FCB4680>
                     └ <function get_request_handler.<locals>.app at 0x000002B10FA489A0>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\fastapi\routing.py", line 302, in app
    raw_response = await run_endpoint_function(
                         └ <function run_endpoint_function at 0x000002B10AC044A0>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\fastapi\routing.py", line 213, in run_endpoint_function
    return await dependant.call(**values)
                 │         │      └ {}
                 │         └ <function knowledge_base_health_check at 0x000002B10F42EDE0>
                 └ Dependant(path_params=[], query_params=[], header_params=[], cookie_params=[], body_params=[], dependencies=[], security_requ...

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\app\iot\api\v1\knowledge_base.py", line 65, in knowledge_base_health_check
    return await response_base.fail(
                 │             └ <function ResponseBase.fail at 0x000002B10C7F4FE0>
                 └ <backend.common.response.response_schema.ResponseBase object at 0x000002B10C7D6330>

TypeError: ResponseBase.fail() got an unexpected keyword argument 'code'
2025-08-04 16:20:59.213 | ERROR    | 88f7ef4a5d3a42ff84619cc3deca2daf | 请求异常: ResponseBase.fail() got an unexpected keyword argument 'code'
2025-08-04 16:20:59.213 | ERROR    | 88f7ef4a5d3a42ff84619cc3deca2daf | Exception in ASGI application

Traceback (most recent call last):

  File "<string>", line 1, in <module>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\multiprocessing\spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 372
               │     └ 3
               └ <function _main at 0x000002B106C07B00>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\multiprocessing\spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 372
           │    └ <function BaseProcess._bootstrap at 0x000002B106B07A60>
           └ <SpawnProcess name='SpawnProcess-1' parent=34812 started>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\multiprocessing\process.py", line 314, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x000002B106B06FC0>
    └ <SpawnProcess name='SpawnProcess-1' parent=34812 started>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x000002B10900E870>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-1' parent=34812 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-1' parent=34812 started>
    │    └ <function subprocess_started at 0x000002B10909C5E0>
    └ <SpawnProcess name='SpawnProcess-1' parent=34812 started>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=484, family=2, type=1, proto=6, laddr=('127.0.0.1', 8000)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x000002B1091C2CF0>>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=484, family=2, type=1, proto=6, laddr=('127.0.0.1', 8000)>]
           │       │   │    └ <function Server.serve at 0x000002B10907FB00>
           │       │   └ <uvicorn.server.Server object at 0x000002B1091C2CF0>
           │       └ <function run at 0x000002B1067D0680>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 194, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x000002B1091AACE0>
           │      └ <function Runner.run at 0x000002B108D80B80>
           └ <asyncio.runners.Runner object at 0x000002B106B81070>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-p...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x000002B108D7E660>
           │    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x000002B106B81070>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 674, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x000002B108D7E5C0>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 641, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000002B108D80400>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1986, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000002B108D18360>
    └ <Handle Task.task_wakeup(<Future finished result=None>)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future finished result=None>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future finished result=None>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future finished result=None>)>
> File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\protocols\http\httptools_impl.py", line 409, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
                   └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x000002B1091C3E90>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\middleware\proxy_headers.py", line 60, in __call__
    return await self.app(scope, receive, send)
                 │    │   │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002B10F...
                 │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002B...
                 │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
                 │    └ <fastapi.applications.FastAPI object at 0x000002B10D0EDD00>
                 └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x000002B1091C3E90>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\fastapi\applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
                           │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002B10F...
                           │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002B...
                           └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\applications.py", line 113, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002B10F...
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002B...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000002B10F94C230>
          └ <fastapi.applications.FastAPI object at 0x000002B10D0EDD00>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\errors.py", line 186, in __call__
    raise exc
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000002B10FCFBD80>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002B...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ CorrelationIdMiddleware(app=<backend.middleware.access_middleware.AccessMiddleware object at 0x000002B10F9D8FB0>, header_name...
          └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000002B10F94C230>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\asgi_correlation_id\middleware.py", line 90, in __call__
    await self.app(scope, receive, handle_outgoing_request)
          │    │   │      │        └ <function CorrelationIdMiddleware.__call__.<locals>.handle_outgoing_request at 0x000002B10FCFBCE0>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002B...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <backend.middleware.access_middleware.AccessMiddleware object at 0x000002B10F9D8FB0>
          └ CorrelationIdMiddleware(app=<backend.middleware.access_middleware.AccessMiddleware object at 0x000002B10F9D8FB0>, header_name...
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 182, in __call__
    with recv_stream, send_stream, collapse_excgroups():
         │            │            └ <function collapse_excgroups at 0x000002B10AAFD9E0>
         │            └ MemoryObjectSendStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_receive...
         └ MemoryObjectReceiveStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_rece...
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\contextlib.py", line 158, in __exit__
    self.gen.throw(value)
    │    │   │     └ ExceptionGroup('unhandled errors in a TaskGroup', [TypeError("ResponseBase.fail() got an unexpected keyword argument 'code'")])
    │    │   └ <method 'throw' of 'generator' objects>
    │    └ <generator object collapse_excgroups at 0x000002B10FCDD700>
    └ <contextlib._GeneratorContextManager object at 0x000002B10FCF7710>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\_utils.py", line 83, in collapse_excgroups
    raise exc
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 184, in __call__
    response = await self.dispatch_func(request, call_next)
                     │    │             │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x000002B10FCFB380>
                     │    │             └ <starlette.middleware.base._CachedRequest object at 0x000002B10FCF75C0>
                     │    └ <bound method AccessMiddleware.dispatch of <backend.middleware.access_middleware.AccessMiddleware object at 0x000002B10F9D8FB0>>
                     └ <backend.middleware.access_middleware.AccessMiddleware object at 0x000002B10F9D8FB0>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\middleware\access_middleware.py", line 34, in dispatch
    response = await call_next(request)
                     │         └ <starlette.middleware.base._CachedRequest object at 0x000002B10FCF75C0>
                     └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x000002B10FCFB380>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 159, in call_next
    raise app_exc
          └ TypeError("ResponseBase.fail() got an unexpected keyword argument 'code'")
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x000002B10FD0C040>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002B10FCFBE20>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x000002B10F51C4D0>
          └ <backend.middleware.access_middleware.AccessMiddleware object at 0x000002B10F9D8FB0>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\cors.py", line 85, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x000002B10FD0C040>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002B10FCFBE20>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.authentication.AuthenticationMiddleware object at 0x000002B10F51D220>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x000002B10F51C4D0>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\authentication.py", line 48, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x000002B10FD0C040>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002B10FCFBE20>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <backend.middleware.state_middleware.StateMiddleware object at 0x000002B10FA5C7A0>
          └ <starlette.middleware.authentication.AuthenticationMiddleware object at 0x000002B10F51D220>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 182, in __call__
    with recv_stream, send_stream, collapse_excgroups():
         │            │            └ <function collapse_excgroups at 0x000002B10AAFD9E0>
         │            └ MemoryObjectSendStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_receive...
         └ MemoryObjectReceiveStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_rece...
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\contextlib.py", line 158, in __exit__
    self.gen.throw(value)
    │    │   │     └ ExceptionGroup('unhandled errors in a TaskGroup', [TypeError("ResponseBase.fail() got an unexpected keyword argument 'code'")])
    │    │   └ <method 'throw' of 'generator' objects>
    │    └ <generator object collapse_excgroups at 0x000002B10FCDE9B0>
    └ <contextlib._GeneratorContextManager object at 0x000002B10FCF7CE0>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\_utils.py", line 83, in collapse_excgroups
    raise exc
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 184, in __call__
    response = await self.dispatch_func(request, call_next)
                     │    │             │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x000002B10FD0C220>
                     │    │             └ <starlette.middleware.base._CachedRequest object at 0x000002B10FCF7B90>
                     │    └ <bound method StateMiddleware.dispatch of <backend.middleware.state_middleware.StateMiddleware object at 0x000002B10FA5C7A0>>
                     └ <backend.middleware.state_middleware.StateMiddleware object at 0x000002B10FA5C7A0>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\middleware\state_middleware.py", line 32, in dispatch
    response = await call_next(request)
                     │         └ <starlette.middleware.base._CachedRequest object at 0x000002B10FCF7B90>
                     └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x000002B10FD0C220>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 159, in call_next
    raise app_exc
          └ TypeError("ResponseBase.fail() got an unexpected keyword argument 'code'")
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x000002B10FD0C4A0>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002B10FD0C540>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <backend.middleware.opera_log_middleware.OperaLogMiddleware object at 0x000002B10FA5E8D0>
          └ <backend.middleware.state_middleware.StateMiddleware object at 0x000002B10FA5C7A0>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 182, in __call__
    with recv_stream, send_stream, collapse_excgroups():
         │            │            └ <function collapse_excgroups at 0x000002B10AAFD9E0>
         │            └ MemoryObjectSendStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_receive...
         └ MemoryObjectReceiveStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_rece...
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\contextlib.py", line 158, in __exit__
    self.gen.throw(value)
    │    │   │     └ ExceptionGroup('unhandled errors in a TaskGroup', [TypeError("ResponseBase.fail() got an unexpected keyword argument 'code'")])
    │    │   └ <method 'throw' of 'generator' objects>
    │    └ <generator object collapse_excgroups at 0x000002B10FCDECF0>
    └ <contextlib._GeneratorContextManager object at 0x000002B10FD203B0>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\_utils.py", line 83, in collapse_excgroups
    raise exc
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 184, in __call__
    response = await self.dispatch_func(request, call_next)
                     │    │             │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x000002B10FD0C7C0>
                     │    │             └ <starlette.middleware.base._CachedRequest object at 0x000002B10FD20260>
                     │    └ <bound method OperaLogMiddleware.dispatch of <backend.middleware.opera_log_middleware.OperaLogMiddleware object at 0x000002B1...
                     └ <backend.middleware.opera_log_middleware.OperaLogMiddleware object at 0x000002B10FA5E8D0>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\middleware\opera_log_middleware.py", line 115, in dispatch
    raise error from None
          └ TypeError("ResponseBase.fail() got an unexpected keyword argument 'code'")

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\middleware\opera_log_middleware.py", line 51, in dispatch
    response = await call_next(request)
                     │         └ <starlette.middleware.base._CachedRequest object at 0x000002B10FD20260>
                     └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x000002B10FD0C7C0>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 159, in call_next
    raise app_exc
          └ TypeError("ResponseBase.fail() got an unexpected keyword argument 'code'")
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x000002B10FD0CF40>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002B10FD0CFE0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000002B10F93C560>
          └ <backend.middleware.opera_log_middleware.OperaLogMiddleware object at 0x000002B10FA5E8D0>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\exceptions.py", line 63, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x000002B10FD0CF40>
          │                            │    │    │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002B10FD0CFE0>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x000002B10FD20860>
          │                            │    └ <fastapi.routing.APIRouter object at 0x000002B10DA4C3B0>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000002B10F93C560>
          └ <function wrap_app_handling_exceptions at 0x000002B10ABE3420>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002B10FD0CD60>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002B10FD0CFE0>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          └ <fastapi.routing.APIRouter object at 0x000002B10DA4C3B0>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\routing.py", line 716, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002B10FD0CD60>
          │    │                │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002B10FD0CFE0>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x000002B10DA4C3B0>>
          └ <fastapi.routing.APIRouter object at 0x000002B10DA4C3B0>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\routing.py", line 736, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002B10FD0CD60>
          │     │      │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002B10FD0CFE0>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │     └ <function Route.handle at 0x000002B10AC049A0>
          └ APIRoute(path='/api/v1/iot/kb/health', name='knowledge_base_health_check', methods=['GET'])
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\routing.py", line 290, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002B10FD0CD60>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002B10FD0CFE0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <function request_response.<locals>.app at 0x000002B10FA48A40>
          └ APIRoute(path='/api/v1/iot/kb/health', name='knowledge_base_health_check', methods=['GET'])
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\routing.py", line 78, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
          │                            │    │        │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002B10FD0CD60>
          │                            │    │        │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002B10FD0CFE0>
          │                            │    │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │                            │    └ <starlette.requests.Request object at 0x000002B10FD20A70>
          │                            └ <function request_response.<locals>.app.<locals>.app at 0x000002B10FD0D080>
          └ <function wrap_app_handling_exceptions at 0x000002B10ABE3420>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002B10FD0D1C0>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002B10FD0CFE0>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          └ <function request_response.<locals>.app.<locals>.app at 0x000002B10FD0D080>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\routing.py", line 75, in app
    response = await f(request)
                     │ └ <starlette.requests.Request object at 0x000002B10FD20A70>
                     └ <function get_request_handler.<locals>.app at 0x000002B10FA489A0>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\fastapi\routing.py", line 302, in app
    raw_response = await run_endpoint_function(
                         └ <function run_endpoint_function at 0x000002B10AC044A0>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\fastapi\routing.py", line 213, in run_endpoint_function
    return await dependant.call(**values)
                 │         │      └ {}
                 │         └ <function knowledge_base_health_check at 0x000002B10F42EDE0>
                 └ Dependant(path_params=[], query_params=[], header_params=[], cookie_params=[], body_params=[], dependencies=[], security_requ...

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\app\iot\api\v1\knowledge_base.py", line 65, in knowledge_base_health_check
    return await response_base.fail(
                 │             └ <function ResponseBase.fail at 0x000002B10C7F4FE0>
                 └ <backend.common.response.response_schema.ResponseBase object at 0x000002B10C7D6330>

TypeError: ResponseBase.fail() got an unexpected keyword argument 'code'
2025-08-04 16:21:55.161 | ERROR    | b7f21ae58ea54954bd20c0ecc8614969 | 请求异常: ResponseBase.fail() got an unexpected keyword argument 'code'
2025-08-04 16:21:55.162 | ERROR    | b7f21ae58ea54954bd20c0ecc8614969 | Exception in ASGI application

Traceback (most recent call last):

  File "<string>", line 1, in <module>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\multiprocessing\spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 372
               │     └ 3
               └ <function _main at 0x000002B106C07B00>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\multiprocessing\spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 372
           │    └ <function BaseProcess._bootstrap at 0x000002B106B07A60>
           └ <SpawnProcess name='SpawnProcess-1' parent=34812 started>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\multiprocessing\process.py", line 314, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x000002B106B06FC0>
    └ <SpawnProcess name='SpawnProcess-1' parent=34812 started>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x000002B10900E870>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-1' parent=34812 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-1' parent=34812 started>
    │    └ <function subprocess_started at 0x000002B10909C5E0>
    └ <SpawnProcess name='SpawnProcess-1' parent=34812 started>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=484, family=2, type=1, proto=6, laddr=('127.0.0.1', 8000)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x000002B1091C2CF0>>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=484, family=2, type=1, proto=6, laddr=('127.0.0.1', 8000)>]
           │       │   │    └ <function Server.serve at 0x000002B10907FB00>
           │       │   └ <uvicorn.server.Server object at 0x000002B1091C2CF0>
           │       └ <function run at 0x000002B1067D0680>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 194, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x000002B1091AACE0>
           │      └ <function Runner.run at 0x000002B108D80B80>
           └ <asyncio.runners.Runner object at 0x000002B106B81070>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-p...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x000002B108D7E660>
           │    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x000002B106B81070>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 674, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x000002B108D7E5C0>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 641, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000002B108D80400>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1986, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000002B108D18360>
    └ <Handle Task.task_wakeup(<Future finished result=None>)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future finished result=None>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future finished result=None>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future finished result=None>)>
> File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\protocols\http\httptools_impl.py", line 409, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
                   └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x000002B1091C3E90>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\middleware\proxy_headers.py", line 60, in __call__
    return await self.app(scope, receive, send)
                 │    │   │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002B10F...
                 │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002B...
                 │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
                 │    └ <fastapi.applications.FastAPI object at 0x000002B10D0EDD00>
                 └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x000002B1091C3E90>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\fastapi\applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
                           │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002B10F...
                           │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002B...
                           └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\applications.py", line 113, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002B10F...
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002B...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000002B10F94C230>
          └ <fastapi.applications.FastAPI object at 0x000002B10D0EDD00>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\errors.py", line 186, in __call__
    raise exc
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000002B10FC79B20>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002B...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ CorrelationIdMiddleware(app=<backend.middleware.access_middleware.AccessMiddleware object at 0x000002B10F9D8FB0>, header_name...
          └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000002B10F94C230>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\asgi_correlation_id\middleware.py", line 90, in __call__
    await self.app(scope, receive, handle_outgoing_request)
          │    │   │      │        └ <function CorrelationIdMiddleware.__call__.<locals>.handle_outgoing_request at 0x000002B10FD0CCC0>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002B...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <backend.middleware.access_middleware.AccessMiddleware object at 0x000002B10F9D8FB0>
          └ CorrelationIdMiddleware(app=<backend.middleware.access_middleware.AccessMiddleware object at 0x000002B10F9D8FB0>, header_name...
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 182, in __call__
    with recv_stream, send_stream, collapse_excgroups():
         │            │            └ <function collapse_excgroups at 0x000002B10AAFD9E0>
         │            └ MemoryObjectSendStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_receive...
         └ MemoryObjectReceiveStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_rece...
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\contextlib.py", line 158, in __exit__
    self.gen.throw(value)
    │    │   │     └ ExceptionGroup('unhandled errors in a TaskGroup', [TypeError("ResponseBase.fail() got an unexpected keyword argument 'code'")])
    │    │   └ <method 'throw' of 'generator' objects>
    │    └ <generator object collapse_excgroups at 0x000002B10FCDF780>
    └ <contextlib._GeneratorContextManager object at 0x000002B10FD21610>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\_utils.py", line 83, in collapse_excgroups
    raise exc
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 184, in __call__
    response = await self.dispatch_func(request, call_next)
                     │    │             │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x000002B10FD0DB20>
                     │    │             └ <starlette.middleware.base._CachedRequest object at 0x000002B10FCB4D10>
                     │    └ <bound method AccessMiddleware.dispatch of <backend.middleware.access_middleware.AccessMiddleware object at 0x000002B10F9D8FB0>>
                     └ <backend.middleware.access_middleware.AccessMiddleware object at 0x000002B10F9D8FB0>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\middleware\access_middleware.py", line 34, in dispatch
    response = await call_next(request)
                     │         └ <starlette.middleware.base._CachedRequest object at 0x000002B10FCB4D10>
                     └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x000002B10FD0DB20>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 159, in call_next
    raise app_exc
          └ TypeError("ResponseBase.fail() got an unexpected keyword argument 'code'")
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x000002B10FD0D300>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002B10FD0D3A0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x000002B10F51C4D0>
          └ <backend.middleware.access_middleware.AccessMiddleware object at 0x000002B10F9D8FB0>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\cors.py", line 85, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x000002B10FD0D300>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002B10FD0D3A0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.authentication.AuthenticationMiddleware object at 0x000002B10F51D220>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x000002B10F51C4D0>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\authentication.py", line 48, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x000002B10FD0D300>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002B10FD0D3A0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <backend.middleware.state_middleware.StateMiddleware object at 0x000002B10FA5C7A0>
          └ <starlette.middleware.authentication.AuthenticationMiddleware object at 0x000002B10F51D220>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 182, in __call__
    with recv_stream, send_stream, collapse_excgroups():
         │            │            └ <function collapse_excgroups at 0x000002B10AAFD9E0>
         │            └ MemoryObjectSendStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_receive...
         └ MemoryObjectReceiveStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_rece...
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\contextlib.py", line 158, in __exit__
    self.gen.throw(value)
    │    │   │     └ ExceptionGroup('unhandled errors in a TaskGroup', [TypeError("ResponseBase.fail() got an unexpected keyword argument 'code'")])
    │    │   └ <method 'throw' of 'generator' objects>
    │    └ <generator object collapse_excgroups at 0x000002B10FCDF440>
    └ <contextlib._GeneratorContextManager object at 0x000002B10FD21D60>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\_utils.py", line 83, in collapse_excgroups
    raise exc
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 184, in __call__
    response = await self.dispatch_func(request, call_next)
                     │    │             │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x000002B10FD0D4E0>
                     │    │             └ <starlette.middleware.base._CachedRequest object at 0x000002B10FD225D0>
                     │    └ <bound method StateMiddleware.dispatch of <backend.middleware.state_middleware.StateMiddleware object at 0x000002B10FA5C7A0>>
                     └ <backend.middleware.state_middleware.StateMiddleware object at 0x000002B10FA5C7A0>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\middleware\state_middleware.py", line 32, in dispatch
    response = await call_next(request)
                     │         └ <starlette.middleware.base._CachedRequest object at 0x000002B10FD225D0>
                     └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x000002B10FD0D4E0>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 159, in call_next
    raise app_exc
          └ TypeError("ResponseBase.fail() got an unexpected keyword argument 'code'")
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x000002B10FB48680>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002B10FD0D580>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <backend.middleware.opera_log_middleware.OperaLogMiddleware object at 0x000002B10FA5E8D0>
          └ <backend.middleware.state_middleware.StateMiddleware object at 0x000002B10FA5C7A0>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 182, in __call__
    with recv_stream, send_stream, collapse_excgroups():
         │            │            └ <function collapse_excgroups at 0x000002B10AAFD9E0>
         │            └ MemoryObjectSendStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_receive...
         └ MemoryObjectReceiveStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_rece...
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\contextlib.py", line 158, in __exit__
    self.gen.throw(value)
    │    │   │     └ ExceptionGroup('unhandled errors in a TaskGroup', [TypeError("ResponseBase.fail() got an unexpected keyword argument 'code'")])
    │    │   └ <method 'throw' of 'generator' objects>
    │    └ <generator object collapse_excgroups at 0x000002B10FCDF920>
    └ <contextlib._GeneratorContextManager object at 0x000002B10FD23B90>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\_utils.py", line 83, in collapse_excgroups
    raise exc
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 184, in __call__
    response = await self.dispatch_func(request, call_next)
                     │    │             │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x000002B10FE25800>
                     │    │             └ <starlette.middleware.base._CachedRequest object at 0x000002B10FD23A40>
                     │    └ <bound method OperaLogMiddleware.dispatch of <backend.middleware.opera_log_middleware.OperaLogMiddleware object at 0x000002B1...
                     └ <backend.middleware.opera_log_middleware.OperaLogMiddleware object at 0x000002B10FA5E8D0>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\middleware\opera_log_middleware.py", line 115, in dispatch
    raise error from None
          └ TypeError("ResponseBase.fail() got an unexpected keyword argument 'code'")

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\middleware\opera_log_middleware.py", line 51, in dispatch
    response = await call_next(request)
                     │         └ <starlette.middleware.base._CachedRequest object at 0x000002B10FD23A40>
                     └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x000002B10FE25800>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 159, in call_next
    raise app_exc
          └ TypeError("ResponseBase.fail() got an unexpected keyword argument 'code'")
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x000002B10FE25EE0>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002B10FE25F80>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000002B10F93C560>
          └ <backend.middleware.opera_log_middleware.OperaLogMiddleware object at 0x000002B10FA5E8D0>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\exceptions.py", line 63, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x000002B10FE25EE0>
          │                            │    │    │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002B10FE25F80>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x000002B10FB2BD70>
          │                            │    └ <fastapi.routing.APIRouter object at 0x000002B10DA4C3B0>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000002B10F93C560>
          └ <function wrap_app_handling_exceptions at 0x000002B10ABE3420>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002B10FE26020>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002B10FE25F80>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          └ <fastapi.routing.APIRouter object at 0x000002B10DA4C3B0>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\routing.py", line 716, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002B10FE26020>
          │    │                │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002B10FE25F80>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x000002B10DA4C3B0>>
          └ <fastapi.routing.APIRouter object at 0x000002B10DA4C3B0>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\routing.py", line 736, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002B10FE26020>
          │     │      │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002B10FE25F80>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │     └ <function Route.handle at 0x000002B10AC049A0>
          └ APIRoute(path='/api/v1/iot/kb/health', name='knowledge_base_health_check', methods=['GET'])
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\routing.py", line 290, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002B10FE26020>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002B10FE25F80>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <function request_response.<locals>.app at 0x000002B10FA48A40>
          └ APIRoute(path='/api/v1/iot/kb/health', name='knowledge_base_health_check', methods=['GET'])
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\routing.py", line 78, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
          │                            │    │        │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002B10FE26020>
          │                            │    │        │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002B10FE25F80>
          │                            │    │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │                            │    └ <starlette.requests.Request object at 0x000002B10FEA0200>
          │                            └ <function request_response.<locals>.app.<locals>.app at 0x000002B10FE260C0>
          └ <function wrap_app_handling_exceptions at 0x000002B10ABE3420>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002B10FE26200>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002B10FE25F80>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          └ <function request_response.<locals>.app.<locals>.app at 0x000002B10FE260C0>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\routing.py", line 75, in app
    response = await f(request)
                     │ └ <starlette.requests.Request object at 0x000002B10FEA0200>
                     └ <function get_request_handler.<locals>.app at 0x000002B10FA489A0>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\fastapi\routing.py", line 302, in app
    raw_response = await run_endpoint_function(
                         └ <function run_endpoint_function at 0x000002B10AC044A0>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\fastapi\routing.py", line 213, in run_endpoint_function
    return await dependant.call(**values)
                 │         │      └ {}
                 │         └ <function knowledge_base_health_check at 0x000002B10F42EDE0>
                 └ Dependant(path_params=[], query_params=[], header_params=[], cookie_params=[], body_params=[], dependencies=[], security_requ...

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\app\iot\api\v1\knowledge_base.py", line 65, in knowledge_base_health_check
    return await response_base.fail(
                 │             └ <function ResponseBase.fail at 0x000002B10C7F4FE0>
                 └ <backend.common.response.response_schema.ResponseBase object at 0x000002B10C7D6330>

TypeError: ResponseBase.fail() got an unexpected keyword argument 'code'
2025-08-04 16:24:12.461 | ERROR    | 729ef9cd7a29428c8bb088caba469e17 | 请求异常: Not Authenticated
2025-08-04 16:24:12.486 | ERROR    | 5c8bdd03f4104bc682ea144a1feb2a7a | 请求异常: Not Authenticated
2025-08-04 16:39:18.918 | ERROR    | 44bca8afd15f4fc1a32b5746c30dcd8b | 请求异常: Not Authenticated
2025-08-04 16:39:18.957 | ERROR    | 5918e99be90b4093ac70f24043671377 | 请求异常: Not Authenticated
2025-08-04 16:39:25.773 | ERROR    | cc11b5f3bc844020bcd23ace47501748 | 请求异常: Not Authenticated
2025-08-04 16:39:25.777 | ERROR    | 4c9d912ebcef4dc0ba604c34690a3985 | 请求异常: Not Authenticated
2025-08-04 16:40:58.583 | ERROR    | 6cbaba16abce4f7385f1325b8666822b | 请求异常: Not Authenticated
2025-08-04 16:40:58.584 | ERROR    | 422e8d32d68b450fb2f34acf14410ddc | 请求异常: Not Authenticated
2025-08-04 16:41:04.968 | ERROR    | 98ec9dac00834b9c8a5691e2cd755b7d | 请求异常: ResponseBase.fail() got an unexpected keyword argument 'code'
2025-08-04 16:41:04.969 | ERROR    | 98ec9dac00834b9c8a5691e2cd755b7d | Exception in ASGI application

Traceback (most recent call last):

  File "<string>", line 1, in <module>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\multiprocessing\spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 380
               │     └ 3
               └ <function _main at 0x000001BDBF9DBB00>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\multiprocessing\spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 380
           │    └ <function BaseProcess._bootstrap at 0x000001BDBF8D7A60>
           └ <SpawnProcess name='SpawnProcess-1' parent=35296 started>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\multiprocessing\process.py", line 314, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x000001BDBF8D6FC0>
    └ <SpawnProcess name='SpawnProcess-1' parent=35296 started>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x000001BDC1D896D0>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-1' parent=35296 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-1' parent=35296 started>
    │    └ <function subprocess_started at 0x000001BDC1DEC5E0>
    └ <SpawnProcess name='SpawnProcess-1' parent=35296 started>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=400, family=2, type=1, proto=6, laddr=('127.0.0.1', 8000)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x000001BDC1F32DE0>>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=400, family=2, type=1, proto=6, laddr=('127.0.0.1', 8000)>]
           │       │   │    └ <function Server.serve at 0x000001BDC1DCFB00>
           │       │   └ <uvicorn.server.Server object at 0x000001BDC1F32DE0>
           │       └ <function run at 0x000001BDBF580680>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 194, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x000001BDC1F1ACE0>
           │      └ <function Runner.run at 0x000001BDC1AB0B80>
           └ <asyncio.runners.Runner object at 0x000001BDC1E5CD40>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-p...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x000001BDC1AAE660>
           │    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x000001BDC1E5CD40>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 674, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x000001BDC1AAE5C0>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 641, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000001BDC1AB0400>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1986, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000001BDC1A48360>
    └ <Handle Task.task_wakeup(<Future finished result=None>)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future finished result=None>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future finished result=None>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future finished result=None>)>
> File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\protocols\http\httptools_impl.py", line 409, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
                   └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x000001BDC1F32C00>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\middleware\proxy_headers.py", line 60, in __call__
    return await self.app(scope, receive, send)
                 │    │   │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000001BDC8...
                 │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000001B...
                 │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
                 │    └ <fastapi.applications.FastAPI object at 0x000001BDC64BDF10>
                 └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x000001BDC1F32C00>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\fastapi\applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
                           │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000001BDC8...
                           │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000001B...
                           └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\applications.py", line 113, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000001BDC8...
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000001B...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000001BDC8889580>
          └ <fastapi.applications.FastAPI object at 0x000001BDC64BDF10>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\errors.py", line 186, in __call__
    raise exc
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000001BDC889AA20>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000001B...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ CorrelationIdMiddleware(app=<backend.middleware.access_middleware.AccessMiddleware object at 0x000001BDC8533020>, header_name...
          └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000001BDC8889580>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\asgi_correlation_id\middleware.py", line 90, in __call__
    await self.app(scope, receive, handle_outgoing_request)
          │    │   │      │        └ <function CorrelationIdMiddleware.__call__.<locals>.handle_outgoing_request at 0x000001BDC88999E0>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000001B...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <backend.middleware.access_middleware.AccessMiddleware object at 0x000001BDC8533020>
          └ CorrelationIdMiddleware(app=<backend.middleware.access_middleware.AccessMiddleware object at 0x000001BDC8533020>, header_name...
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 182, in __call__
    with recv_stream, send_stream, collapse_excgroups():
         │            │            └ <function collapse_excgroups at 0x000001BDC38DD9E0>
         │            └ MemoryObjectSendStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_receive...
         └ MemoryObjectReceiveStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_rece...
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\contextlib.py", line 158, in __exit__
    self.gen.throw(value)
    │    │   │     └ ExceptionGroup('unhandled errors in a TaskGroup', [TypeError("ResponseBase.fail() got an unexpected keyword argument 'code'")])
    │    │   └ <method 'throw' of 'generator' objects>
    │    └ <generator object collapse_excgroups at 0x000001BDC886EC20>
    └ <contextlib._GeneratorContextManager object at 0x000001BDC892A510>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\_utils.py", line 83, in collapse_excgroups
    raise exc
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 184, in __call__
    response = await self.dispatch_func(request, call_next)
                     │    │             │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x000001BDC8A26980>
                     │    │             └ <starlette.middleware.base._CachedRequest object at 0x000001BDC892B080>
                     │    └ <bound method AccessMiddleware.dispatch of <backend.middleware.access_middleware.AccessMiddleware object at 0x000001BDC8533020>>
                     └ <backend.middleware.access_middleware.AccessMiddleware object at 0x000001BDC8533020>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\middleware\access_middleware.py", line 34, in dispatch
    response = await call_next(request)
                     │         └ <starlette.middleware.base._CachedRequest object at 0x000001BDC892B080>
                     └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x000001BDC8A26980>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 159, in call_next
    raise app_exc
          └ TypeError("ResponseBase.fail() got an unexpected keyword argument 'code'")
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x000001BDC8A139C0>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001BDC8A26A20>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x000001BDC883FA70>
          └ <backend.middleware.access_middleware.AccessMiddleware object at 0x000001BDC8533020>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\cors.py", line 93, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
          │    │               │      │        │                     └ Headers({'host': 'localhost:8000', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"Windows"', 'user-agent': 'Mozilla/5.0 ...
          │    │               │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x000001BDC8A139C0>
          │    │               │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001BDC8A26A20>
          │    │               └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <function CORSMiddleware.simple_response at 0x000001BDC4A2D440>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x000001BDC883FA70>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\cors.py", line 144, in simple_response
    await self.app(scope, receive, send)
          │    │   │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x000001BDC883FA70...
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001BDC8A26A20>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.authentication.AuthenticationMiddleware object at 0x000001BDC883D580>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x000001BDC883FA70>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\authentication.py", line 48, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x000001BDC883FA70...
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001BDC8A26A20>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <backend.middleware.state_middleware.StateMiddleware object at 0x000001BDC883C110>
          └ <starlette.middleware.authentication.AuthenticationMiddleware object at 0x000001BDC883D580>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 182, in __call__
    with recv_stream, send_stream, collapse_excgroups():
         │            │            └ <function collapse_excgroups at 0x000001BDC38DD9E0>
         │            └ MemoryObjectSendStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_receive...
         └ MemoryObjectReceiveStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_rece...
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\contextlib.py", line 158, in __exit__
    self.gen.throw(value)
    │    │   │     └ ExceptionGroup('unhandled errors in a TaskGroup', [TypeError("ResponseBase.fail() got an unexpected keyword argument 'code'")])
    │    │   └ <method 'throw' of 'generator' objects>
    │    └ <generator object collapse_excgroups at 0x000001BDC886D490>
    └ <contextlib._GeneratorContextManager object at 0x000001BDC87F5640>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\_utils.py", line 83, in collapse_excgroups
    raise exc
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 184, in __call__
    response = await self.dispatch_func(request, call_next)
                     │    │             │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x000001BDC8931080>
                     │    │             └ <starlette.middleware.base._CachedRequest object at 0x000001BDC892ACC0>
                     │    └ <bound method StateMiddleware.dispatch of <backend.middleware.state_middleware.StateMiddleware object at 0x000001BDC883C110>>
                     └ <backend.middleware.state_middleware.StateMiddleware object at 0x000001BDC883C110>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\middleware\state_middleware.py", line 32, in dispatch
    response = await call_next(request)
                     │         └ <starlette.middleware.base._CachedRequest object at 0x000001BDC892ACC0>
                     └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x000001BDC8931080>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 159, in call_next
    raise app_exc
          └ TypeError("ResponseBase.fail() got an unexpected keyword argument 'code'")
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x000001BDC8931440>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001BDC8931120>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <backend.middleware.opera_log_middleware.OperaLogMiddleware object at 0x000001BDC87BF2F0>
          └ <backend.middleware.state_middleware.StateMiddleware object at 0x000001BDC883C110>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 182, in __call__
    with recv_stream, send_stream, collapse_excgroups():
         │            │            └ <function collapse_excgroups at 0x000001BDC38DD9E0>
         │            └ MemoryObjectSendStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_receive...
         └ MemoryObjectReceiveStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_rece...
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\contextlib.py", line 158, in __exit__
    self.gen.throw(value)
    │    │   │     └ ExceptionGroup('unhandled errors in a TaskGroup', [TypeError("ResponseBase.fail() got an unexpected keyword argument 'code'")])
    │    │   └ <method 'throw' of 'generator' objects>
    │    └ <generator object collapse_excgroups at 0x000001BDC886FD30>
    └ <contextlib._GeneratorContextManager object at 0x000001BDC8AAAE10>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\_utils.py", line 83, in collapse_excgroups
    raise exc
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 184, in __call__
    response = await self.dispatch_func(request, call_next)
                     │    │             │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x000001BDC889A8E0>
                     │    │             └ <starlette.middleware.base._CachedRequest object at 0x000001BDC8AAAF30>
                     │    └ <bound method OperaLogMiddleware.dispatch of <backend.middleware.opera_log_middleware.OperaLogMiddleware object at 0x000001BD...
                     └ <backend.middleware.opera_log_middleware.OperaLogMiddleware object at 0x000001BDC87BF2F0>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\middleware\opera_log_middleware.py", line 115, in dispatch
    raise error from None
          └ TypeError("ResponseBase.fail() got an unexpected keyword argument 'code'")

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\middleware\opera_log_middleware.py", line 51, in dispatch
    response = await call_next(request)
                     │         └ <starlette.middleware.base._CachedRequest object at 0x000001BDC8AAAF30>
                     └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x000001BDC889A8E0>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 159, in call_next
    raise app_exc
          └ TypeError("ResponseBase.fail() got an unexpected keyword argument 'code'")
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x000001BDC8A9DE40>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001BDC8931B20>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000001BDC87BE630>
          └ <backend.middleware.opera_log_middleware.OperaLogMiddleware object at 0x000001BDC87BF2F0>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\exceptions.py", line 63, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x000001BDC8A9DE40>
          │                            │    │    │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001BDC8931B20>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x000001BDC8AA8680>
          │                            │    └ <fastapi.routing.APIRouter object at 0x000001BDC67C7E00>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000001BDC87BE630>
          └ <function wrap_app_handling_exceptions at 0x000001BDC39C3420>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001BDC8A9E8E0>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001BDC8931B20>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          └ <fastapi.routing.APIRouter object at 0x000001BDC67C7E00>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\routing.py", line 716, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001BDC8A9E8E0>
          │    │                │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001BDC8931B20>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x000001BDC67C7E00>>
          └ <fastapi.routing.APIRouter object at 0x000001BDC67C7E00>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\routing.py", line 736, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001BDC8A9E8E0>
          │     │      │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001BDC8931B20>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │     └ <function Route.handle at 0x000001BDC39E49A0>
          └ APIRoute(path='/api/v1/iot/kb/health', name='knowledge_base_health_check', methods=['GET'])
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\routing.py", line 290, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001BDC8A9E8E0>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001BDC8931B20>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <function request_response.<locals>.app at 0x000001BDC882D1C0>
          └ APIRoute(path='/api/v1/iot/kb/health', name='knowledge_base_health_check', methods=['GET'])
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\routing.py", line 78, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
          │                            │    │        │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001BDC8A9E8E0>
          │                            │    │        │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001BDC8931B20>
          │                            │    │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │                            │    └ <starlette.requests.Request object at 0x000001BDC8AA8A40>
          │                            └ <function request_response.<locals>.app.<locals>.app at 0x000001BDC8A9DC60>
          └ <function wrap_app_handling_exceptions at 0x000001BDC39C3420>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001BDC8A9C720>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001BDC8931B20>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          └ <function request_response.<locals>.app.<locals>.app at 0x000001BDC8A9DC60>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\routing.py", line 75, in app
    response = await f(request)
                     │ └ <starlette.requests.Request object at 0x000001BDC8AA8A40>
                     └ <function get_request_handler.<locals>.app at 0x000001BDC882D120>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\fastapi\routing.py", line 302, in app
    raw_response = await run_endpoint_function(
                         └ <function run_endpoint_function at 0x000001BDC39E44A0>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\fastapi\routing.py", line 213, in run_endpoint_function
    return await dependant.call(**values)
                 │         │      └ {}
                 │         └ <function knowledge_base_health_check at 0x000001BDC820F1A0>
                 └ Dependant(path_params=[], query_params=[], header_params=[], cookie_params=[], body_params=[], dependencies=[], security_requ...

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\app\iot\api\v1\knowledge_base.py", line 65, in knowledge_base_health_check
    return await response_base.fail(
                 │             └ <function ResponseBase.fail at 0x000001BDC5590FE0>
                 └ <backend.common.response.response_schema.ResponseBase object at 0x000001BDC5566180>

TypeError: ResponseBase.fail() got an unexpected keyword argument 'code'
2025-08-04 16:41:05.997 | ERROR    | dc70ae5b05ff463694749eec4404002a | 请求异常: ResponseBase.fail() got an unexpected keyword argument 'code'
2025-08-04 16:41:05.999 | ERROR    | dc70ae5b05ff463694749eec4404002a | Exception in ASGI application

Traceback (most recent call last):

  File "<string>", line 1, in <module>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\multiprocessing\spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 380
               │     └ 3
               └ <function _main at 0x000001BDBF9DBB00>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\multiprocessing\spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 380
           │    └ <function BaseProcess._bootstrap at 0x000001BDBF8D7A60>
           └ <SpawnProcess name='SpawnProcess-1' parent=35296 started>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\multiprocessing\process.py", line 314, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x000001BDBF8D6FC0>
    └ <SpawnProcess name='SpawnProcess-1' parent=35296 started>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x000001BDC1D896D0>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-1' parent=35296 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-1' parent=35296 started>
    │    └ <function subprocess_started at 0x000001BDC1DEC5E0>
    └ <SpawnProcess name='SpawnProcess-1' parent=35296 started>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=400, family=2, type=1, proto=6, laddr=('127.0.0.1', 8000)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x000001BDC1F32DE0>>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=400, family=2, type=1, proto=6, laddr=('127.0.0.1', 8000)>]
           │       │   │    └ <function Server.serve at 0x000001BDC1DCFB00>
           │       │   └ <uvicorn.server.Server object at 0x000001BDC1F32DE0>
           │       └ <function run at 0x000001BDBF580680>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 194, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x000001BDC1F1ACE0>
           │      └ <function Runner.run at 0x000001BDC1AB0B80>
           └ <asyncio.runners.Runner object at 0x000001BDC1E5CD40>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-p...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x000001BDC1AAE660>
           │    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x000001BDC1E5CD40>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 674, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x000001BDC1AAE5C0>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 641, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000001BDC1AB0400>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1986, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000001BDC1A48360>
    └ <Handle Task.task_wakeup(<Future finished result=None>)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future finished result=None>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future finished result=None>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future finished result=None>)>
> File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\protocols\http\httptools_impl.py", line 409, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
                   └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x000001BDC1F32C00>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\middleware\proxy_headers.py", line 60, in __call__
    return await self.app(scope, receive, send)
                 │    │   │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000001BDC8...
                 │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000001B...
                 │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
                 │    └ <fastapi.applications.FastAPI object at 0x000001BDC64BDF10>
                 └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x000001BDC1F32C00>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\fastapi\applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
                           │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000001BDC8...
                           │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000001B...
                           └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\applications.py", line 113, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000001BDC8...
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000001B...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000001BDC8889580>
          └ <fastapi.applications.FastAPI object at 0x000001BDC64BDF10>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\errors.py", line 186, in __call__
    raise exc
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000001BDC8A9E7A0>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000001B...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ CorrelationIdMiddleware(app=<backend.middleware.access_middleware.AccessMiddleware object at 0x000001BDC8533020>, header_name...
          └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000001BDC8889580>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\asgi_correlation_id\middleware.py", line 90, in __call__
    await self.app(scope, receive, handle_outgoing_request)
          │    │   │      │        └ <function CorrelationIdMiddleware.__call__.<locals>.handle_outgoing_request at 0x000001BDC8A6E200>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000001B...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <backend.middleware.access_middleware.AccessMiddleware object at 0x000001BDC8533020>
          └ CorrelationIdMiddleware(app=<backend.middleware.access_middleware.AccessMiddleware object at 0x000001BDC8533020>, header_name...
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 182, in __call__
    with recv_stream, send_stream, collapse_excgroups():
         │            │            └ <function collapse_excgroups at 0x000001BDC38DD9E0>
         │            └ MemoryObjectSendStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_receive...
         └ MemoryObjectReceiveStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_rece...
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\contextlib.py", line 158, in __exit__
    self.gen.throw(value)
    │    │   │     └ ExceptionGroup('unhandled errors in a TaskGroup', [TypeError("ResponseBase.fail() got an unexpected keyword argument 'code'")])
    │    │   └ <method 'throw' of 'generator' objects>
    │    └ <generator object collapse_excgroups at 0x000001BDC8ACE810>
    └ <contextlib._GeneratorContextManager object at 0x000001BDC8AE55B0>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\_utils.py", line 83, in collapse_excgroups
    raise exc
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 184, in __call__
    response = await self.dispatch_func(request, call_next)
                     │    │             │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x000001BDC8AF8720>
                     │    │             └ <starlette.middleware.base._CachedRequest object at 0x000001BDBF5D7020>
                     │    └ <bound method AccessMiddleware.dispatch of <backend.middleware.access_middleware.AccessMiddleware object at 0x000001BDC8533020>>
                     └ <backend.middleware.access_middleware.AccessMiddleware object at 0x000001BDC8533020>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\middleware\access_middleware.py", line 34, in dispatch
    response = await call_next(request)
                     │         └ <starlette.middleware.base._CachedRequest object at 0x000001BDBF5D7020>
                     └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x000001BDC8AF8720>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 159, in call_next
    raise app_exc
          └ TypeError("ResponseBase.fail() got an unexpected keyword argument 'code'")
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x000001BDC8AF8680>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001BDC8AF89A0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x000001BDC883FA70>
          └ <backend.middleware.access_middleware.AccessMiddleware object at 0x000001BDC8533020>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\cors.py", line 93, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
          │    │               │      │        │                     └ Headers({'host': 'localhost:8000', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"Windows"', 'user-agent': 'Mozilla/5.0 ...
          │    │               │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x000001BDC8AF8680>
          │    │               │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001BDC8AF89A0>
          │    │               └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <function CORSMiddleware.simple_response at 0x000001BDC4A2D440>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x000001BDC883FA70>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\cors.py", line 144, in simple_response
    await self.app(scope, receive, send)
          │    │   │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x000001BDC883FA70...
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001BDC8AF89A0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.authentication.AuthenticationMiddleware object at 0x000001BDC883D580>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x000001BDC883FA70>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\authentication.py", line 48, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x000001BDC883FA70...
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001BDC8AF89A0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <backend.middleware.state_middleware.StateMiddleware object at 0x000001BDC883C110>
          └ <starlette.middleware.authentication.AuthenticationMiddleware object at 0x000001BDC883D580>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 182, in __call__
    with recv_stream, send_stream, collapse_excgroups():
         │            │            └ <function collapse_excgroups at 0x000001BDC38DD9E0>
         │            └ MemoryObjectSendStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_receive...
         └ MemoryObjectReceiveStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_rece...
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\contextlib.py", line 158, in __exit__
    self.gen.throw(value)
    │    │   │     └ ExceptionGroup('unhandled errors in a TaskGroup', [TypeError("ResponseBase.fail() got an unexpected keyword argument 'code'")])
    │    │   └ <method 'throw' of 'generator' objects>
    │    └ <generator object collapse_excgroups at 0x000001BDC8ACEDC0>
    └ <contextlib._GeneratorContextManager object at 0x000001BDC8AE6900>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\_utils.py", line 83, in collapse_excgroups
    raise exc
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 184, in __call__
    response = await self.dispatch_func(request, call_next)
                     │    │             │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x000001BDC8AF8C20>
                     │    │             └ <starlette.middleware.base._CachedRequest object at 0x000001BDC8AE53A0>
                     │    └ <bound method StateMiddleware.dispatch of <backend.middleware.state_middleware.StateMiddleware object at 0x000001BDC883C110>>
                     └ <backend.middleware.state_middleware.StateMiddleware object at 0x000001BDC883C110>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\middleware\state_middleware.py", line 32, in dispatch
    response = await call_next(request)
                     │         └ <starlette.middleware.base._CachedRequest object at 0x000001BDC8AE53A0>
                     └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x000001BDC8AF8C20>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 159, in call_next
    raise app_exc
          └ TypeError("ResponseBase.fail() got an unexpected keyword argument 'code'")
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x000001BDC8AF8B80>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001BDC8AF8400>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <backend.middleware.opera_log_middleware.OperaLogMiddleware object at 0x000001BDC87BF2F0>
          └ <backend.middleware.state_middleware.StateMiddleware object at 0x000001BDC883C110>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 182, in __call__
    with recv_stream, send_stream, collapse_excgroups():
         │            │            └ <function collapse_excgroups at 0x000001BDC38DD9E0>
         │            └ MemoryObjectSendStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_receive...
         └ MemoryObjectReceiveStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_rece...
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\contextlib.py", line 158, in __exit__
    self.gen.throw(value)
    │    │   │     └ ExceptionGroup('unhandled errors in a TaskGroup', [TypeError("ResponseBase.fail() got an unexpected keyword argument 'code'")])
    │    │   └ <method 'throw' of 'generator' objects>
    │    └ <generator object collapse_excgroups at 0x000001BDC8ACEE90>
    └ <contextlib._GeneratorContextManager object at 0x000001BDC8AE6F60>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\_utils.py", line 83, in collapse_excgroups
    raise exc
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 184, in __call__
    response = await self.dispatch_func(request, call_next)
                     │    │             │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x000001BDC8AF8860>
                     │    │             └ <starlette.middleware.base._CachedRequest object at 0x000001BDC8AE6E10>
                     │    └ <bound method OperaLogMiddleware.dispatch of <backend.middleware.opera_log_middleware.OperaLogMiddleware object at 0x000001BD...
                     └ <backend.middleware.opera_log_middleware.OperaLogMiddleware object at 0x000001BDC87BF2F0>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\middleware\opera_log_middleware.py", line 115, in dispatch
    raise error from None
          └ TypeError("ResponseBase.fail() got an unexpected keyword argument 'code'")

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\middleware\opera_log_middleware.py", line 51, in dispatch
    response = await call_next(request)
                     │         └ <starlette.middleware.base._CachedRequest object at 0x000001BDC8AE6E10>
                     └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x000001BDC8AF8860>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 159, in call_next
    raise app_exc
          └ TypeError("ResponseBase.fail() got an unexpected keyword argument 'code'")
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x000001BDC8C35300>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001BDC8C353A0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000001BDC87BE630>
          └ <backend.middleware.opera_log_middleware.OperaLogMiddleware object at 0x000001BDC87BF2F0>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\exceptions.py", line 63, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x000001BDC8C35300>
          │                            │    │    │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001BDC8C353A0>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x000001BDC8AE7440>
          │                            │    └ <fastapi.routing.APIRouter object at 0x000001BDC67C7E00>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000001BDC87BE630>
          └ <function wrap_app_handling_exceptions at 0x000001BDC39C3420>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001BDC8C35440>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001BDC8C353A0>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          └ <fastapi.routing.APIRouter object at 0x000001BDC67C7E00>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\routing.py", line 716, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001BDC8C35440>
          │    │                │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001BDC8C353A0>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x000001BDC67C7E00>>
          └ <fastapi.routing.APIRouter object at 0x000001BDC67C7E00>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\routing.py", line 736, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001BDC8C35440>
          │     │      │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001BDC8C353A0>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │     └ <function Route.handle at 0x000001BDC39E49A0>
          └ APIRoute(path='/api/v1/iot/kb/health', name='knowledge_base_health_check', methods=['GET'])
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\routing.py", line 290, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001BDC8C35440>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001BDC8C353A0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <function request_response.<locals>.app at 0x000001BDC882D1C0>
          └ APIRoute(path='/api/v1/iot/kb/health', name='knowledge_base_health_check', methods=['GET'])
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\routing.py", line 78, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
          │                            │    │        │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001BDC8C35440>
          │                            │    │        │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001BDC8C353A0>
          │                            │    │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │                            │    └ <starlette.requests.Request object at 0x000001BDC8AE7620>
          │                            └ <function request_response.<locals>.app.<locals>.app at 0x000001BDC8C354E0>
          └ <function wrap_app_handling_exceptions at 0x000001BDC39C3420>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001BDC8C35620>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001BDC8C353A0>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          └ <function request_response.<locals>.app.<locals>.app at 0x000001BDC8C354E0>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\routing.py", line 75, in app
    response = await f(request)
                     │ └ <starlette.requests.Request object at 0x000001BDC8AE7620>
                     └ <function get_request_handler.<locals>.app at 0x000001BDC882D120>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\fastapi\routing.py", line 302, in app
    raw_response = await run_endpoint_function(
                         └ <function run_endpoint_function at 0x000001BDC39E44A0>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\fastapi\routing.py", line 213, in run_endpoint_function
    return await dependant.call(**values)
                 │         │      └ {}
                 │         └ <function knowledge_base_health_check at 0x000001BDC820F1A0>
                 └ Dependant(path_params=[], query_params=[], header_params=[], cookie_params=[], body_params=[], dependencies=[], security_requ...

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\app\iot\api\v1\knowledge_base.py", line 65, in knowledge_base_health_check
    return await response_base.fail(
                 │             └ <function ResponseBase.fail at 0x000001BDC5590FE0>
                 └ <backend.common.response.response_schema.ResponseBase object at 0x000001BDC5566180>

TypeError: ResponseBase.fail() got an unexpected keyword argument 'code'
2025-08-04 16:41:06.248 | ERROR    | 6b537856ef6245a6b6fc7b4d379995a8 | 请求异常: ResponseBase.fail() got an unexpected keyword argument 'code'
2025-08-04 16:41:06.250 | ERROR    | 6b537856ef6245a6b6fc7b4d379995a8 | Exception in ASGI application

Traceback (most recent call last):

  File "<string>", line 1, in <module>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\multiprocessing\spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 380
               │     └ 3
               └ <function _main at 0x000001BDBF9DBB00>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\multiprocessing\spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 380
           │    └ <function BaseProcess._bootstrap at 0x000001BDBF8D7A60>
           └ <SpawnProcess name='SpawnProcess-1' parent=35296 started>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\multiprocessing\process.py", line 314, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x000001BDBF8D6FC0>
    └ <SpawnProcess name='SpawnProcess-1' parent=35296 started>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x000001BDC1D896D0>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-1' parent=35296 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-1' parent=35296 started>
    │    └ <function subprocess_started at 0x000001BDC1DEC5E0>
    └ <SpawnProcess name='SpawnProcess-1' parent=35296 started>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=400, family=2, type=1, proto=6, laddr=('127.0.0.1', 8000)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x000001BDC1F32DE0>>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=400, family=2, type=1, proto=6, laddr=('127.0.0.1', 8000)>]
           │       │   │    └ <function Server.serve at 0x000001BDC1DCFB00>
           │       │   └ <uvicorn.server.Server object at 0x000001BDC1F32DE0>
           │       └ <function run at 0x000001BDBF580680>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 194, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x000001BDC1F1ACE0>
           │      └ <function Runner.run at 0x000001BDC1AB0B80>
           └ <asyncio.runners.Runner object at 0x000001BDC1E5CD40>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-p...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x000001BDC1AAE660>
           │    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x000001BDC1E5CD40>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 674, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x000001BDC1AAE5C0>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 641, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000001BDC1AB0400>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1986, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000001BDC1A48360>
    └ <Handle Task.task_wakeup(<Future finished result=None>)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future finished result=None>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future finished result=None>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future finished result=None>)>
> File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\protocols\http\httptools_impl.py", line 409, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
                   └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x000001BDC1F32C00>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\middleware\proxy_headers.py", line 60, in __call__
    return await self.app(scope, receive, send)
                 │    │   │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000001BDC8...
                 │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000001B...
                 │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
                 │    └ <fastapi.applications.FastAPI object at 0x000001BDC64BDF10>
                 └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x000001BDC1F32C00>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\fastapi\applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
                           │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000001BDC8...
                           │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000001B...
                           └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\applications.py", line 113, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000001BDC8...
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000001B...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000001BDC8889580>
          └ <fastapi.applications.FastAPI object at 0x000001BDC64BDF10>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\errors.py", line 186, in __call__
    raise exc
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000001BDC8AF8AE0>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000001B...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ CorrelationIdMiddleware(app=<backend.middleware.access_middleware.AccessMiddleware object at 0x000001BDC8533020>, header_name...
          └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000001BDC8889580>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\asgi_correlation_id\middleware.py", line 90, in __call__
    await self.app(scope, receive, handle_outgoing_request)
          │    │   │      │        └ <function CorrelationIdMiddleware.__call__.<locals>.handle_outgoing_request at 0x000001BDC8C35A80>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000001B...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <backend.middleware.access_middleware.AccessMiddleware object at 0x000001BDC8533020>
          └ CorrelationIdMiddleware(app=<backend.middleware.access_middleware.AccessMiddleware object at 0x000001BDC8533020>, header_name...
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 182, in __call__
    with recv_stream, send_stream, collapse_excgroups():
         │            │            └ <function collapse_excgroups at 0x000001BDC38DD9E0>
         │            └ MemoryObjectSendStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_receive...
         └ MemoryObjectReceiveStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_rece...
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\contextlib.py", line 158, in __exit__
    self.gen.throw(value)
    │    │   │     └ ExceptionGroup('unhandled errors in a TaskGroup', [TypeError("ResponseBase.fail() got an unexpected keyword argument 'code'")])
    │    │   └ <method 'throw' of 'generator' objects>
    │    └ <generator object collapse_excgroups at 0x000001BDC8ACEA80>
    └ <contextlib._GeneratorContextManager object at 0x000001BDC8C74B60>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\_utils.py", line 83, in collapse_excgroups
    raise exc
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 184, in __call__
    response = await self.dispatch_func(request, call_next)
                     │    │             │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x000001BDC8C35B20>
                     │    │             └ <starlette.middleware.base._CachedRequest object at 0x000001BDC8AE7E00>
                     │    └ <bound method AccessMiddleware.dispatch of <backend.middleware.access_middleware.AccessMiddleware object at 0x000001BDC8533020>>
                     └ <backend.middleware.access_middleware.AccessMiddleware object at 0x000001BDC8533020>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\middleware\access_middleware.py", line 34, in dispatch
    response = await call_next(request)
                     │         └ <starlette.middleware.base._CachedRequest object at 0x000001BDC8AE7E00>
                     └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x000001BDC8C35B20>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 159, in call_next
    raise app_exc
          └ TypeError("ResponseBase.fail() got an unexpected keyword argument 'code'")
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x000001BDC8C35760>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001BDC8C358A0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x000001BDC883FA70>
          └ <backend.middleware.access_middleware.AccessMiddleware object at 0x000001BDC8533020>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\cors.py", line 93, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
          │    │               │      │        │                     └ Headers({'host': 'localhost:8000', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"Windows"', 'user-agent': 'Mozilla/5.0 ...
          │    │               │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x000001BDC8C35760>
          │    │               │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001BDC8C358A0>
          │    │               └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <function CORSMiddleware.simple_response at 0x000001BDC4A2D440>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x000001BDC883FA70>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\cors.py", line 144, in simple_response
    await self.app(scope, receive, send)
          │    │   │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x000001BDC883FA70...
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001BDC8C358A0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.authentication.AuthenticationMiddleware object at 0x000001BDC883D580>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x000001BDC883FA70>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\authentication.py", line 48, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x000001BDC883FA70...
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001BDC8C358A0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <backend.middleware.state_middleware.StateMiddleware object at 0x000001BDC883C110>
          └ <starlette.middleware.authentication.AuthenticationMiddleware object at 0x000001BDC883D580>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 182, in __call__
    with recv_stream, send_stream, collapse_excgroups():
         │            │            └ <function collapse_excgroups at 0x000001BDC38DD9E0>
         │            └ MemoryObjectSendStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_receive...
         └ MemoryObjectReceiveStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_rece...
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\contextlib.py", line 158, in __exit__
    self.gen.throw(value)
    │    │   │     └ ExceptionGroup('unhandled errors in a TaskGroup', [TypeError("ResponseBase.fail() got an unexpected keyword argument 'code'")])
    │    │   └ <method 'throw' of 'generator' objects>
    │    └ <generator object collapse_excgroups at 0x000001BDC8C69150>
    └ <contextlib._GeneratorContextManager object at 0x000001BDC8C74E90>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\_utils.py", line 83, in collapse_excgroups
    raise exc
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 184, in __call__
    response = await self.dispatch_func(request, call_next)
                     │    │             │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x000001BDC8A9CFE0>
                     │    │             └ <starlette.middleware.base._CachedRequest object at 0x000001BDC8C74AD0>
                     │    └ <bound method StateMiddleware.dispatch of <backend.middleware.state_middleware.StateMiddleware object at 0x000001BDC883C110>>
                     └ <backend.middleware.state_middleware.StateMiddleware object at 0x000001BDC883C110>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\middleware\state_middleware.py", line 32, in dispatch
    response = await call_next(request)
                     │         └ <starlette.middleware.base._CachedRequest object at 0x000001BDC8C74AD0>
                     └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x000001BDC8A9CFE0>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 159, in call_next
    raise app_exc
          └ TypeError("ResponseBase.fail() got an unexpected keyword argument 'code'")
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x000001BDC8C35940>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001BDC8C35DA0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <backend.middleware.opera_log_middleware.OperaLogMiddleware object at 0x000001BDC87BF2F0>
          └ <backend.middleware.state_middleware.StateMiddleware object at 0x000001BDC883C110>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 182, in __call__
    with recv_stream, send_stream, collapse_excgroups():
         │            │            └ <function collapse_excgroups at 0x000001BDC38DD9E0>
         │            └ MemoryObjectSendStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_receive...
         └ MemoryObjectReceiveStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_rece...
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\contextlib.py", line 158, in __exit__
    self.gen.throw(value)
    │    │   │     └ ExceptionGroup('unhandled errors in a TaskGroup', [TypeError("ResponseBase.fail() got an unexpected keyword argument 'code'")])
    │    │   └ <method 'throw' of 'generator' objects>
    │    └ <generator object collapse_excgroups at 0x000001BDC8C69490>
    └ <contextlib._GeneratorContextManager object at 0x000001BDC8C74890>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\_utils.py", line 83, in collapse_excgroups
    raise exc
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 184, in __call__
    response = await self.dispatch_func(request, call_next)
                     │    │             │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x000001BDC8C35120>
                     │    │             └ <starlette.middleware.base._CachedRequest object at 0x000001BDC8C743B0>
                     │    └ <bound method OperaLogMiddleware.dispatch of <backend.middleware.opera_log_middleware.OperaLogMiddleware object at 0x000001BD...
                     └ <backend.middleware.opera_log_middleware.OperaLogMiddleware object at 0x000001BDC87BF2F0>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\middleware\opera_log_middleware.py", line 115, in dispatch
    raise error from None
          └ TypeError("ResponseBase.fail() got an unexpected keyword argument 'code'")

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\middleware\opera_log_middleware.py", line 51, in dispatch
    response = await call_next(request)
                     │         └ <starlette.middleware.base._CachedRequest object at 0x000001BDC8C743B0>
                     └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x000001BDC8C35120>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 159, in call_next
    raise app_exc
          └ TypeError("ResponseBase.fail() got an unexpected keyword argument 'code'")
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x000001BDC8C36160>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001BDC8C34EA0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000001BDC87BE630>
          └ <backend.middleware.opera_log_middleware.OperaLogMiddleware object at 0x000001BDC87BF2F0>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\exceptions.py", line 63, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x000001BDC8C36160>
          │                            │    │    │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001BDC8C34EA0>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x000001BDC8C75670>
          │                            │    └ <fastapi.routing.APIRouter object at 0x000001BDC67C7E00>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000001BDC87BE630>
          └ <function wrap_app_handling_exceptions at 0x000001BDC39C3420>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001BDC8C362A0>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001BDC8C34EA0>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          └ <fastapi.routing.APIRouter object at 0x000001BDC67C7E00>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\routing.py", line 716, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001BDC8C362A0>
          │    │                │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001BDC8C34EA0>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x000001BDC67C7E00>>
          └ <fastapi.routing.APIRouter object at 0x000001BDC67C7E00>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\routing.py", line 736, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001BDC8C362A0>
          │     │      │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001BDC8C34EA0>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │     └ <function Route.handle at 0x000001BDC39E49A0>
          └ APIRoute(path='/api/v1/iot/kb/health', name='knowledge_base_health_check', methods=['GET'])
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\routing.py", line 290, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001BDC8C362A0>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001BDC8C34EA0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <function request_response.<locals>.app at 0x000001BDC882D1C0>
          └ APIRoute(path='/api/v1/iot/kb/health', name='knowledge_base_health_check', methods=['GET'])
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\routing.py", line 78, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
          │                            │    │        │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001BDC8C362A0>
          │                            │    │        │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001BDC8C34EA0>
          │                            │    │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │                            │    └ <starlette.requests.Request object at 0x000001BDC8C75880>
          │                            └ <function request_response.<locals>.app.<locals>.app at 0x000001BDC8C36340>
          └ <function wrap_app_handling_exceptions at 0x000001BDC39C3420>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001BDC8C36480>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001BDC8C34EA0>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          └ <function request_response.<locals>.app.<locals>.app at 0x000001BDC8C36340>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\routing.py", line 75, in app
    response = await f(request)
                     │ └ <starlette.requests.Request object at 0x000001BDC8C75880>
                     └ <function get_request_handler.<locals>.app at 0x000001BDC882D120>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\fastapi\routing.py", line 302, in app
    raw_response = await run_endpoint_function(
                         └ <function run_endpoint_function at 0x000001BDC39E44A0>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\fastapi\routing.py", line 213, in run_endpoint_function
    return await dependant.call(**values)
                 │         │      └ {}
                 │         └ <function knowledge_base_health_check at 0x000001BDC820F1A0>
                 └ Dependant(path_params=[], query_params=[], header_params=[], cookie_params=[], body_params=[], dependencies=[], security_requ...

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\app\iot\api\v1\knowledge_base.py", line 65, in knowledge_base_health_check
    return await response_base.fail(
                 │             └ <function ResponseBase.fail at 0x000001BDC5590FE0>
                 └ <backend.common.response.response_schema.ResponseBase object at 0x000001BDC5566180>

TypeError: ResponseBase.fail() got an unexpected keyword argument 'code'
2025-08-04 17:17:37.998 | ERROR    | 232d91eb8a5a49e58be14c33111e4ae5 | 请求异常: object ResponseModel can't be used in 'await' expression
2025-08-04 17:17:38.023 | ERROR    | 232d91eb8a5a49e58be14c33111e4ae5 | Exception in ASGI application

Traceback (most recent call last):

  File "<string>", line 1, in <module>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\multiprocessing\spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 172
               │     └ 3
               └ <function _main at 0x0000020E7185BB00>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\multiprocessing\spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 172
           │    └ <function BaseProcess._bootstrap at 0x0000020E71757A60>
           └ <SpawnProcess name='SpawnProcess-1' parent=31056 started>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\multiprocessing\process.py", line 314, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x0000020E71756FC0>
    └ <SpawnProcess name='SpawnProcess-1' parent=31056 started>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x0000020E73D0E990>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-1' parent=31056 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-1' parent=31056 started>
    │    └ <function subprocess_started at 0x0000020E73C2C5E0>
    └ <SpawnProcess name='SpawnProcess-1' parent=31056 started>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=796, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x0000020E73D76C60>>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=796, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
           │       │   │    └ <function Server.serve at 0x0000020E73C0FB00>
           │       │   └ <uvicorn.server.Server object at 0x0000020E73D76C60>
           │       └ <function run at 0x0000020E714D0680>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 194, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x0000020E73D5ACE0>
           │      └ <function Runner.run at 0x0000020E734A0B80>
           └ <asyncio.runners.Runner object at 0x0000020E73C2B560>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-p...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x0000020E7349E660>
           │    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x0000020E73C2B560>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 674, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x0000020E7349E5C0>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 641, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000020E734A0400>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1986, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x0000020E73438360>
    └ <Handle Task.task_wakeup(<Future finished result=None>)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future finished result=None>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future finished result=None>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future finished result=None>)>
> File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\protocols\http\httptools_impl.py", line 409, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
                   └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x0000020E7A661B20>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\middleware\proxy_headers.py", line 60, in __call__
    return await self.app(scope, receive, send)
                 │    │   │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000020E7A...
                 │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000020...
                 │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
                 │    └ <fastapi.applications.FastAPI object at 0x0000020E78607B90>
                 └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x0000020E7A661B20>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\fastapi\applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
                           │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000020E7A...
                           │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000020...
                           └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\applications.py", line 113, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000020E7A...
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000020...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x0000020E7A6789B0>
          └ <fastapi.applications.FastAPI object at 0x0000020E78607B90>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\errors.py", line 186, in __call__
    raise exc
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x0000020E7A6DEA20>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000020...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ CorrelationIdMiddleware(app=<backend.middleware.access_middleware.AccessMiddleware object at 0x0000020E7A4E0650>, header_name...
          └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x0000020E7A6789B0>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\asgi_correlation_id\middleware.py", line 90, in __call__
    await self.app(scope, receive, handle_outgoing_request)
          │    │   │      │        └ <function CorrelationIdMiddleware.__call__.<locals>.handle_outgoing_request at 0x0000020E7A76A3E0>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000020...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <backend.middleware.access_middleware.AccessMiddleware object at 0x0000020E7A4E0650>
          └ CorrelationIdMiddleware(app=<backend.middleware.access_middleware.AccessMiddleware object at 0x0000020E7A4E0650>, header_name...
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 182, in __call__
    with recv_stream, send_stream, collapse_excgroups():
         │            │            └ <function collapse_excgroups at 0x0000020E7571D9E0>
         │            └ MemoryObjectSendStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_receive...
         └ MemoryObjectReceiveStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_rece...
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\contextlib.py", line 158, in __exit__
    self.gen.throw(value)
    │    │   │     └ ExceptionGroup('unhandled errors in a TaskGroup', [TypeError("object ResponseModel can't be used in 'await' expression")])
    │    │   └ <method 'throw' of 'generator' objects>
    │    └ <generator object collapse_excgroups at 0x0000020E7A6B5F20>
    └ <contextlib._GeneratorContextManager object at 0x0000020E7A6E3110>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\_utils.py", line 83, in collapse_excgroups
    raise exc
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 184, in __call__
    response = await self.dispatch_func(request, call_next)
                     │    │             │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x0000020E7A4ECE00>
                     │    │             └ <starlette.middleware.base._CachedRequest object at 0x0000020E7A6E2E70>
                     │    └ <bound method AccessMiddleware.dispatch of <backend.middleware.access_middleware.AccessMiddleware object at 0x0000020E7A4E0650>>
                     └ <backend.middleware.access_middleware.AccessMiddleware object at 0x0000020E7A4E0650>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\middleware\access_middleware.py", line 34, in dispatch
    response = await call_next(request)
                     │         └ <starlette.middleware.base._CachedRequest object at 0x0000020E7A6E2E70>
                     └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x0000020E7A4ECE00>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 159, in call_next
    raise app_exc
          └ TypeError("object ResponseModel can't be used in 'await' expression")
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000020E7A774540>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000020E7A775300>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x0000020E7A4E1DF0>
          └ <backend.middleware.access_middleware.AccessMiddleware object at 0x0000020E7A4E0650>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\cors.py", line 85, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000020E7A774540>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000020E7A775300>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.authentication.AuthenticationMiddleware object at 0x0000020E79F16CF0>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x0000020E7A4E1DF0>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\authentication.py", line 48, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000020E7A774540>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000020E7A775300>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <backend.middleware.state_middleware.StateMiddleware object at 0x0000020E7A663500>
          └ <starlette.middleware.authentication.AuthenticationMiddleware object at 0x0000020E79F16CF0>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 182, in __call__
    with recv_stream, send_stream, collapse_excgroups():
         │            │            └ <function collapse_excgroups at 0x0000020E7571D9E0>
         │            └ MemoryObjectSendStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_receive...
         └ MemoryObjectReceiveStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_rece...
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\contextlib.py", line 158, in __exit__
    self.gen.throw(value)
    │    │   │     └ ExceptionGroup('unhandled errors in a TaskGroup', [TypeError("object ResponseModel can't be used in 'await' expression")])
    │    │   └ <method 'throw' of 'generator' objects>
    │    └ <generator object collapse_excgroups at 0x0000020E7A6B6B50>
    └ <contextlib._GeneratorContextManager object at 0x0000020E7A76D580>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\_utils.py", line 83, in collapse_excgroups
    raise exc
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 184, in __call__
    response = await self.dispatch_func(request, call_next)
                     │    │             │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x0000020E7A7745E0>
                     │    │             └ <starlette.middleware.base._CachedRequest object at 0x0000020E7A76D190>
                     │    └ <bound method StateMiddleware.dispatch of <backend.middleware.state_middleware.StateMiddleware object at 0x0000020E7A663500>>
                     └ <backend.middleware.state_middleware.StateMiddleware object at 0x0000020E7A663500>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\middleware\state_middleware.py", line 32, in dispatch
    response = await call_next(request)
                     │         └ <starlette.middleware.base._CachedRequest object at 0x0000020E7A76D190>
                     └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x0000020E7A7745E0>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 159, in call_next
    raise app_exc
          └ TypeError("object ResponseModel can't be used in 'await' expression")
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000020E7A7747C0>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000020E7A6DDD00>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <backend.middleware.opera_log_middleware.OperaLogMiddleware object at 0x0000020E79716FF0>
          └ <backend.middleware.state_middleware.StateMiddleware object at 0x0000020E7A663500>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 182, in __call__
    with recv_stream, send_stream, collapse_excgroups():
         │            │            └ <function collapse_excgroups at 0x0000020E7571D9E0>
         │            └ MemoryObjectSendStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_receive...
         └ MemoryObjectReceiveStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_rece...
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\contextlib.py", line 158, in __exit__
    self.gen.throw(value)
    │    │   │     └ ExceptionGroup('unhandled errors in a TaskGroup', [TypeError("object ResponseModel can't be used in 'await' expression")])
    │    │   └ <method 'throw' of 'generator' objects>
    │    └ <generator object collapse_excgroups at 0x0000020E7A6B79F0>
    └ <contextlib._GeneratorContextManager object at 0x0000020E7A76DF10>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\_utils.py", line 83, in collapse_excgroups
    raise exc
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 184, in __call__
    response = await self.dispatch_func(request, call_next)
                     │    │             │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x0000020E7A774A40>
                     │    │             └ <starlette.middleware.base._CachedRequest object at 0x0000020E7A76DC70>
                     │    └ <bound method OperaLogMiddleware.dispatch of <backend.middleware.opera_log_middleware.OperaLogMiddleware object at 0x0000020E...
                     └ <backend.middleware.opera_log_middleware.OperaLogMiddleware object at 0x0000020E79716FF0>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\middleware\opera_log_middleware.py", line 115, in dispatch
    raise error from None
          └ TypeError("object ResponseModel can't be used in 'await' expression")

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\middleware\opera_log_middleware.py", line 51, in dispatch
    response = await call_next(request)
                     │         └ <starlette.middleware.base._CachedRequest object at 0x0000020E7A76DC70>
                     └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x0000020E7A774A40>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 159, in call_next
    raise app_exc
          └ TypeError("object ResponseModel can't be used in 'await' expression")
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000020E7A775440>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000020E7A7754E0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000020E7862FFB0>
          └ <backend.middleware.opera_log_middleware.OperaLogMiddleware object at 0x0000020E79716FF0>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\exceptions.py", line 63, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000020E7A775440>
          │                            │    │    │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000020E7A7754E0>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x0000020E7A76E600>
          │                            │    └ <fastapi.routing.APIRouter object at 0x0000020E782AD670>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000020E7862FFB0>
          └ <function wrap_app_handling_exceptions at 0x0000020E75803420>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000020E7A775580>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000020E7A7754E0>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          └ <fastapi.routing.APIRouter object at 0x0000020E782AD670>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\routing.py", line 716, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000020E7A775580>
          │    │                │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000020E7A7754E0>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x0000020E782AD670>>
          └ <fastapi.routing.APIRouter object at 0x0000020E782AD670>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\routing.py", line 736, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000020E7A775580>
          │     │      │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000020E7A7754E0>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │     └ <function Route.handle at 0x0000020E758249A0>
          └ APIRoute(path='/api/v1/iot/kb/health', name='knowledge_base_health_check', methods=['GET'])
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\routing.py", line 290, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000020E7A775580>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000020E7A7754E0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <function request_response.<locals>.app at 0x0000020E7A66D440>
          └ APIRoute(path='/api/v1/iot/kb/health', name='knowledge_base_health_check', methods=['GET'])
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\routing.py", line 78, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
          │                            │    │        │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000020E7A775580>
          │                            │    │        │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000020E7A7754E0>
          │                            │    │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │                            │    └ <starlette.requests.Request object at 0x0000020E7A76E7B0>
          │                            └ <function request_response.<locals>.app.<locals>.app at 0x0000020E7A775620>
          └ <function wrap_app_handling_exceptions at 0x0000020E75803420>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000020E7A775760>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000020E7A7754E0>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          └ <function request_response.<locals>.app.<locals>.app at 0x0000020E7A775620>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\routing.py", line 75, in app
    response = await f(request)
                     │ └ <starlette.requests.Request object at 0x0000020E7A76E7B0>
                     └ <function get_request_handler.<locals>.app at 0x0000020E7A66D3A0>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\fastapi\routing.py", line 302, in app
    raw_response = await run_endpoint_function(
                         └ <function run_endpoint_function at 0x0000020E758244A0>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\fastapi\routing.py", line 213, in run_endpoint_function
    return await dependant.call(**values)
                 │         │      └ {}
                 │         └ <function knowledge_base_health_check at 0x0000020E7A04F060>
                 └ Dependant(path_params=[], query_params=[], header_params=[], cookie_params=[], body_params=[], dependencies=[], security_requ...

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\app\iot\api\v1\knowledge_base.py", line 64, in knowledge_base_health_check
    return await response_base.fail(
                 │             └ <function ResponseBase.fail at 0x0000020E773D4FE0>
                 └ <backend.common.response.response_schema.ResponseBase object at 0x0000020E773B6330>

TypeError: object ResponseModel can't be used in 'await' expression
2025-08-04 18:08:55.548 | ERROR    | - | Exception in callback _ProactorBasePipeTransport._call_connection_lost(None)
handle: <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
Traceback (most recent call last):

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\start_stable.py", line 12, in <module>
    uvicorn.run(
    │       └ <function run at 0x0000024D6BA28AE0>
    └ <module 'uvicorn' from 'C:\\AI\\fastapi_best_arc\\fastapi_best_architecture\\.venv\\Lib\\site-packages\\uvicorn\\__init__.py'>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\main.py", line 580, in run
    server.run()
    │      └ <function Server.run at 0x0000024D6BA2B9C0>
    └ <uvicorn.server.Server object at 0x0000024D721E8710>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x0000024D6BA2BA60>
           │       │   └ <uvicorn.server.Server object at 0x0000024D721E8710>
           │       └ <function run at 0x0000024D6B18AFC0>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 194, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x0000024D723ECD60>
           │      └ <function Runner.run at 0x0000024D6B307240>
           └ <asyncio.runners.Runner object at 0x0000024D71D20170>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-11' coro=<Server.serve() running at C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x0000024D6B304E00>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x0000024D71D20170>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 674, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x0000024D6B3D8CC0>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 641, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000024D6B306B60>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1986, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x0000024D6B1847C0>
    └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\proactor_events.py", line 165, in _call_connection_lost
    self._sock.shutdown(socket.SHUT_RDWR)
    │    │     │        │      └ 2
    │    │     │        └ <module 'socket' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\socket.py'>
    │    │     └ <method 'shutdown' of '_socket.socket' objects>
    │    └ <socket.socket fd=1784, family=2, type=1, proto=0, laddr=('127.0.0.1', 8000), raddr=('127.0.0.1', 56484)>
    └ <_ProactorSocketTransport closing fd=1784>

ConnectionResetError: [WinError 10054] 远程主机强迫关闭了一个现有的连接。
2025-08-04 18:57:35.458 | ERROR    | b8c2e2bf97e147199afc63f4e8a3bdab | JWT 授权异常：1 validation error for GetUserInfoWithRelationDetail
roles
  Field required [type=missing, input_value={'salt': b'$2b$12$8y2eNuc...permissions': ['*:*:*']}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
Traceback (most recent call last):

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\start_stable.py", line 12, in <module>
    uvicorn.run(
    │       └ <function run at 0x000002BBBA9F8AE0>
    └ <module 'uvicorn' from 'C:\\AI\\fastapi_best_arc\\fastapi_best_architecture\\.venv\\Lib\\site-packages\\uvicorn\\__init__.py'>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\main.py", line 580, in run
    server.run()
    │      └ <function Server.run at 0x000002BBBA9FB9C0>
    └ <uvicorn.server.Server object at 0x000002BBC13A84D0>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x000002BBBA9FBA60>
           │       │   └ <uvicorn.server.Server object at 0x000002BBC13A84D0>
           │       └ <function run at 0x000002BBBA21AFC0>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 194, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x000002BBC141CD60>
           │      └ <function Runner.run at 0x000002BBBA2B7240>
           └ <asyncio.runners.Runner object at 0x000002BBC12CD460>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-11' coro=<Server.serve() running at C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x000002BBBA2B4E00>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x000002BBC12CD460>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 674, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x000002BBBA388CC0>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 641, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000002BBBA2B6B60>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1986, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000002BBBA2147C0>
    └ <Handle Task.task_wakeup(<Future finished result=None>)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future finished result=None>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future finished result=None>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future finished result=None>)>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x000002BBC1566840>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002BBC156C180>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x000002BBC14CF2C0>
          └ <backend.middleware.access_middleware.AccessMiddleware object at 0x000002BBC134E690>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\cors.py", line 85, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x000002BBC1566840>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002BBC156C180>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.authentication.AuthenticationMiddleware object at 0x000002BBC14022A0>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x000002BBC14CF2C0>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\authentication.py", line 36, in __call__
    auth_result = await self.backend.authenticate(conn)
                        │    │       │            └ <starlette.requests.HTTPConnection object at 0x000002BBC1512870>
                        │    │       └ <function JwtAuthMiddleware.authenticate at 0x000002BBBE420D60>
                        │    └ <backend.middleware.jwt_auth_middleware.JwtAuthMiddleware object at 0x000002BBC034D2B0>
                        └ <starlette.middleware.authentication.AuthenticationMiddleware object at 0x000002BBC14022A0>

> File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\middleware\jwt_auth_middleware.py", line 74, in authenticate
    user = await jwt_authentication(token)
                 │                  └ 'eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6IjQ1N2I1ZjJjLTY0ODItNDdjMy1iMjJjLTBjZjZhNWNmMzUwOCIsInVzZXJuYW1...
                 └ <function jwt_authentication at 0x000002BBBEF78040>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\common\security\jwt.py", line 336, in jwt_authentication
    return await iot_adapter.authenticate_iot_token(token)
                 │           │                      └ 'eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6IjQ1N2I1ZjJjLTY0ODItNDdjMy1iMjJjLTBjZjZhNWNmMzUwOCIsInVzZXJuYW1...
                 │           └ <function IoTAuthAdapter.authenticate_iot_token at 0x000002BBBEEEB880>
                 └ <backend.common.security.iot_adapter.IoTAuthAdapter object at 0x000002BBBDC2C3E0>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\common\security\iot_adapter.py", line 359, in authenticate_iot_token
    return GetUserInfoWithRelationDetail(**user_dict)
           │                               └ {'salt': b'$2b$12$8y2eNucX19VjmZ3tYhBLcO', 'join_time': datetime.datetime(2025, 7, 30, 9, 57, 15), 'email': '<EMAIL>...
           └ <class 'backend.app.admin.schema.user.GetUserInfoWithRelationDetail'>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\pydantic\main.py", line 253, in __init__
    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)
                     │    │                      │               │                   └ GetUserInfoWithRelationDetail()
                     │    │                      │               └ {'salt': b'$2b$12$8y2eNucX19VjmZ3tYhBLcO', 'join_time': datetime.datetime(2025, 7, 30, 9, 57, 15), 'email': '<EMAIL>...
                     │    │                      └ <method 'validate_python' of 'pydantic_core._pydantic_core.SchemaValidator' objects>
                     │    └ SchemaValidator(title="GetUserInfoWithRelationDetail", validator=Model(
                     │          ModelValidator {
                     │              revalidate: Never,
                     │           ...
                     └ GetUserInfoWithRelationDetail()

pydantic_core._pydantic_core.ValidationError: 1 validation error for GetUserInfoWithRelationDetail
roles
  Field required [type=missing, input_value={'salt': b'$2b$12$8y2eNuc...permissions': ['*:*:*']}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
2025-08-04 18:59:05.626 | ERROR    | 199dc6e49b8f4447b869f049e1391e2e | 请求异常: 请求参数非法: func 字段为必填项，输入：None
