#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
获取当前Java系统中的JWT token
根据Redis中的session_id生成对应的JWT token
"""

import jwt
import redis
import json
from datetime import datetime

# JWT配置 (与Java系统保持一致)
JWT_SECRET = "abcdefghijklfastbeesmartrstuvwxyz"
JWT_ALGORITHM = "HS512"

# Redis配置
REDIS_CONFIG = {
    'host': '127.0.0.1',
    'port': 5862,
    'password': 'tldiot',
    'db': 1,
    'decode_responses': True
}

def connect_redis():
    """连接Redis"""
    try:
        r = redis.Redis(**REDIS_CONFIG)
        r.ping()
        print("✅ Redis连接成功")
        return r
    except Exception as e:
        print(f"❌ Redis连接失败: {e}")
        return None

def find_login_tokens(r):
    """查找所有login token"""
    try:
        # 首先列出所有keys
        all_keys = r.keys("*")
        print(f"\n📊 Redis中总共有 {len(all_keys)} 个keys")

        # 搜索所有可能的login token keys
        patterns = ["login_tokens*", "*login*", "*token*", "*457b5f2c*"]
        relevant_keys = set()

        for pattern in patterns:
            keys = r.keys(pattern)
            relevant_keys.update(keys)
            if keys:
                print(f"模式 '{pattern}' 找到 {len(keys)} 个keys: {keys}")

        # 如果没找到相关keys，显示所有keys
        if not relevant_keys:
            print(f"\n⚠️ 未找到相关keys，显示所有keys:")
            for i, key in enumerate(all_keys[:20]):  # 只显示前20个
                print(f"  {i+1}. {key}")
            if len(all_keys) > 20:
                print(f"  ... 还有 {len(all_keys) - 20} 个keys")

        print(f"\n找到 {len(relevant_keys)} 个相关keys:")

        login_users = []

        # 检查所有相关keys
        for key in relevant_keys:
            try:
                value = r.get(key)
                print(f"\n📋 Key: {key}")
                print(f"📄 Value长度: {len(value) if value else 0} 字符")

                if value:
                    # 显示前200个字符
                    preview = value[:200] + "..." if len(value) > 200 else value
                    print(f"📄 内容预览: {preview}")

                    # 检查是否包含LoginUser或token相关内容
                    if any(keyword in value for keyword in ['LoginUser', 'token', 'username', 'admin']):
                        # 尝试解析JSON
                        try:
                            data = json.loads(value)
                            if 'token' in data:
                                token = data['token']
                                username = data.get('username', 'unknown')
                                expire_time = data.get('expireTime', 0)

                                # 转换过期时间
                                if expire_time:
                                    expire_dt = datetime.fromtimestamp(expire_time / 1000)
                                    expire_str = expire_dt.strftime('%Y-%m-%d %H:%M:%S')
                                else:
                                    expire_str = "未知"

                                login_users.append({
                                    'key': key,
                                    'token': token,
                                    'username': username,
                                    'expire_time': expire_str,
                                    'data': data
                                })

                                print(f"🔑 Token: {token}")
                                print(f"👤 用户: {username}")
                                print(f"⏰ 过期时间: {expire_str}")
                        except json.JSONDecodeError:
                            print(f"⚠️ 无法解析JSON，但包含相关内容")

            except Exception as e:
                print(f"⚠️ 读取key失败 {key}: {e}")

        return login_users

    except Exception as e:
        print(f"❌ 查找login tokens失败: {e}")
        return []

def generate_jwt_token(session_id):
    """根据session_id生成JWT token"""
    try:
        payload = {
            "login_user_key": session_id
        }
        
        token = jwt.encode(
            payload,
            JWT_SECRET,
            algorithm=JWT_ALGORITHM
        )
        
        return token
    except Exception as e:
        print(f"❌ 生成JWT token失败: {e}")
        return None

def main():
    print("=" * 60)
    print("获取当前Java系统中的JWT token")
    print("=" * 60)
    
    # 连接Redis
    r = connect_redis()
    if not r:
        return
    
    # 查找login tokens
    login_users = find_login_tokens(r)
    
    if not login_users:
        print("\n❌ 未找到任何LoginUser数据")
        return
    
    print(f"\n🎯 找到 {len(login_users)} 个活跃用户:")
    print("=" * 60)
    
    for i, user in enumerate(login_users, 1):
        session_id = user['token']
        username = user['username']
        expire_time = user['expire_time']
        
        print(f"\n{i}. 用户: {username}")
        print(f"   Session ID: {session_id}")
        print(f"   过期时间: {expire_time}")
        
        # 生成对应的JWT token
        jwt_token = generate_jwt_token(session_id)
        if jwt_token:
            print(f"   JWT Token: {jwt_token}")
            print(f"   测试命令:")
            print(f'   curl -X GET "http://localhost:8000/api/v1/iot/kb/datasets" \\')
            print(f'     -H "Authorization: Bearer {jwt_token}"')
        else:
            print(f"   ❌ JWT生成失败")
    
    # 如果只有一个用户，提供详细的测试说明
    if len(login_users) == 1:
        user = login_users[0]
        jwt_token = generate_jwt_token(user['token'])
        
        print("\n" + "=" * 60)
        print("🚀 快速测试")
        print("=" * 60)
        print(f"复制以下JWT token进行测试:")
        print(f"\n{jwt_token}\n")
        
        print("测试命令:")
        print(f'curl -X GET "http://localhost:8000/api/v1/iot/kb/datasets" \\')
        print(f'  -H "Authorization: Bearer {jwt_token}" \\')
        print(f'  -H "Content-Type: application/json"')

if __name__ == "__main__":
    main()
