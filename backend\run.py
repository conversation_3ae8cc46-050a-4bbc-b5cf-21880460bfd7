#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import os

import uvicorn

if __name__ == '__main__':
    # 为什么独立此启动文件：https://stackoverflow.com/questions/64003384
    # 如果你喜欢在 IDE 中进行 DEBUG，可在 IDE 中直接右键启动此文件
    # 如果你喜欢通过 print 方式进行调试，建议使用 fastapi cli 方式启动服务
    try:
        uvicorn.run(
            app='backend.main:app',
            host='0.0.0.0',
            port=8000,
            reload=True,
            reload_excludes=[
                '../.venv',
                'test_*.py',  # 排除测试文件
                'check_*.py',  # 排除检查脚本
                '*.log',  # 排除日志文件
                '.history',  # 排除历史文件
                '__pycache__',  # 排除缓存文件
            ],
        )
    except Exception as e:
        raise e
