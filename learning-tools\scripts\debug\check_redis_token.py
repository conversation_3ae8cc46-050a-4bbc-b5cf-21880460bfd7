#!/usr/bin/env python3
"""
检查Redis中的token
"""
import asyncio
import jwt
import base64
import json
from backend.database.redis import redis_client

async def check_redis_token():
    """检查Redis中的token"""
    token = "eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6IjY2MmU0MDEwLTk5MGItNDFjNy05YTkxLTczMmJmNWQ0Mzg2YiJ9.9mbJA3hUDgVMxcbtYJuMd2Ni69CgefIfcLjeG63_xOvN0Zc196TcHBaipBZWkMDDknXT1dcaAbE5YbrbMGokzA"
    
    print("=" * 60)
    print("Redis Token 检查")
    print("=" * 60)
    
    # 解码payload（不验证签名）
    payload_b64 = token.split('.')[1]
    payload_b64 += '=' * (4 - len(payload_b64) % 4)
    payload = json.loads(base64.b64decode(payload_b64))
    print(f"JWT Payload: {payload}")
    
    session_id = payload.get('login_user_key', '')
    print(f"Session ID: {session_id}")
    
    # 检查不同的Redis key格式
    redis_keys = [
        f"login_tokens:{session_id}",
        f"login_userId:{session_id}",
        session_id,
        f"token:{session_id}",
        f"user:{session_id}",
    ]
    
    for redis_key in redis_keys:
        print(f"\n--- 检查Redis Key: {redis_key} ---")
        try:
            cached_data = await redis_client.get(redis_key)
            if cached_data:
                print(f"✅ 找到数据: {len(cached_data)} 字符")
                if len(cached_data) < 200:  # 如果数据不太长，显示内容
                    print(f"内容: {cached_data}")
                else:
                    print(f"内容预览: {cached_data[:100]}...")
                
                # 检查是否是token
                if cached_data == token:
                    print("🎉 找到匹配的token！")
                elif token in cached_data:
                    print("🎉 在数据中找到token！")
            else:
                print("❌ 未找到数据")
        except Exception as e:
            print(f"❌ 错误: {e}")
    
    # 搜索所有可能的keys
    print(f"\n--- 搜索包含session_id的所有keys ---")
    try:
        keys = []
        async for key in redis_client.scan_iter(match=f"*{session_id}*"):
            keys.append(key)
        
        if keys:
            print(f"找到 {len(keys)} 个相关keys:")
            for key in keys[:10]:  # 只显示前10个
                print(f"  - {key}")
        else:
            print("未找到相关keys")
    except Exception as e:
        print(f"搜索错误: {e}")

if __name__ == "__main__":
    asyncio.run(check_redis_token())
