#!/usr/bin/env python3
"""
修复knowledge_base.py中错误的await response_base调用
"""
import re

def fix_await_calls():
    file_path = "backend/app/iot/api/v1/knowledge_base.py"
    
    # 读取文件内容
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 移除response_base方法前的await
    content = re.sub(r'return await response_base\.', 'return response_base.', content)
    
    # 写回文件
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("修复完成！")

if __name__ == "__main__":
    fix_await_calls()
