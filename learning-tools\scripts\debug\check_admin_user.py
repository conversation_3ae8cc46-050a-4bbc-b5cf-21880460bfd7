#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查数据库中的admin用户
"""

import asyncio
from backend.database.db import async_db_session
from backend.app.admin.crud.crud_user import user_dao

async def check_admin_user():
    print("=" * 60)
    print("检查数据库中的用户")
    print("=" * 60)
    
    try:
        async with async_db_session() as db:
            # 查找admin用户
            admin_user = await user_dao.get_by_username(db, "admin")
            
            if admin_user:
                print(f"✅ 找到admin用户:")
                print(f"  ID: {admin_user.id}")
                print(f"  用户名: {admin_user.username}")
                print(f"  昵称: {admin_user.nickname}")
                print(f"  邮箱: {admin_user.email}")
                print(f"  状态: {'正常' if admin_user.status else '锁定'}")
                print(f"  超级管理员: {'是' if admin_user.is_superuser else '否'}")
                print(f"  员工: {'是' if admin_user.is_staff else '否'}")
            else:
                print("❌ 未找到admin用户")
            
            # 查找所有用户
            print(f"\n📋 所有用户列表:")
            users = await user_dao.get_list(db)
            for user in users:
                print(f"  - {user.username} (ID: {user.id}, 状态: {'正常' if user.status else '锁定'})")
                
    except Exception as e:
        print(f"❌ 检查失败: {e}")

if __name__ == "__main__":
    asyncio.run(check_admin_user())
