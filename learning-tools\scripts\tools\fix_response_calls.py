#!/usr/bin/env python3
"""
修复knowledge_base.py中错误的response_base.fail()调用
"""
import re

def fix_response_calls():
    file_path = "backend/app/iot/api/v1/knowledge_base.py"
    
    # 读取文件内容
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 定义替换模式
    patterns = [
        # 模式1: code=数字, msg=字符串
        (
            r'return await response_base\.fail\(\s*code=([^,]+),\s*msg=([^)]+)\s*\)',
            r'return await response_base.fail(\n            res=CustomResponse(code=\1, msg=\2)\n        )'
        ),
        # 模式2: code=数字, msg=字符串, 有其他参数
        (
            r'return await response_base\.fail\(\s*code=([^,]+),\s*msg=([^,]+),\s*([^)]+)\s*\)',
            r'return await response_base.fail(\n            res=CustomResponse(code=\1, msg=\2),\n            \3\n        )'
        )
    ]
    
    # 应用替换
    for pattern, replacement in patterns:
        content = re.sub(pattern, replacement, content, flags=re.MULTILINE | re.DOTALL)
    
    # 写回文件
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("修复完成！")

if __name__ == "__main__":
    fix_response_calls()
