#!/usr/bin/env python3
"""
JWT 密钥测试脚本
测试不同的密钥配置
"""
import jwt
import base64
import json

def test_jwt_keys():
    """测试不同的JWT密钥"""
    token = "eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6IjY2MmU0MDEwLTk5MGItNDFjNy05YTkxLTczMmJmNWQ0Mzg2YiJ9.9mbJA3hUDgVMxcbtYJuMd2Ni69CgefIfcLjeG63_xOvN0Zc196TcHBaipBZWkMDDknXT1dcaAbE5YbrbMGokzA"
    
    print("=" * 60)
    print("JWT 密钥测试")
    print("=" * 60)
    
    # 解码header
    header_b64 = token.split('.')[0]
    header_b64 += '=' * (4 - len(header_b64) % 4)
    header = json.loads(base64.b64decode(header_b64))
    print(f"JWT Header: {header}")
    
    # 解码payload（不验证签名）
    payload_b64 = token.split('.')[1]
    payload_b64 += '=' * (4 - len(payload_b64) % 4)
    payload = json.loads(base64.b64decode(payload_b64))
    print(f"JWT Payload: {payload}")
    
    # 测试不同的密钥
    test_keys = [
        "abcdefghijklfastbeesmartrstuvwxyz",  # 当前配置的密钥
        "fastbee",  # 可能的简单密钥
        "fastbee-secret",  # 可能的密钥
        "TS-IOT-SYS",  # 系统名称
        "your-shared-secret-key",  # 默认密钥
        "",  # 空密钥
    ]
    
    algorithm = header['alg']
    print(f"\n使用算法: {algorithm}")
    
    for i, key in enumerate(test_keys, 1):
        print(f"\n--- 测试密钥 {i}: '{key}' ---")
        try:
            decoded = jwt.decode(
                token,
                key,
                algorithms=[algorithm],
                options={'verify_exp': False}
            )
            print(f"✅ 成功！密钥: '{key}'")
            print(f"解码结果: {decoded}")
            return key
        except jwt.InvalidSignatureError:
            print(f"❌ 签名验证失败")
        except Exception as e:
            print(f"❌ 其他错误: {e}")
    
    print("\n❌ 所有测试密钥都失败了")
    return None

if __name__ == "__main__":
    test_jwt_keys()
